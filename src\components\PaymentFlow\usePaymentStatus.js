import { useState, useEffect, useCallback, useRef } from "react";
import { useLazyQuery } from "@apollo/client";
import {
  PAYMENT_STATUS_QUERY,
  INSTALLMENT_PAYMENT_STATUS_QUERY,
  EXTENSION_PAYMENT_STATUS_QUERY,
} from "./graphql";

const usePaymentStatus = ({
  checkoutId,
  rentalId,
  extensionId = null,
  isInstallment = false,
  onStatusChange,
  onError,
  pollingInterval = 3000, // 3 seconds
  maxPollingAttempts = 20, // 1 minute total
}) => {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pollingCount, setPollingCount] = useState(0);
  const pollingRef = useRef(null);
  const isMountedRef = useRef(true);

  // Choose the appropriate mutation based on payment type
  const statusQuery = extensionId
    ? EXTENSION_PAYMENT_STATUS_QUERY
    : isInstallment
    ? INSTALLMENT_PAYMENT_STATUS_QUERY
    : PAYMENT_STATUS_QUERY;

  const [checkPaymentStatus] = useLazyQuery(statusQuery, {
    errorPolicy: "all",
  });

  const stopPolling = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
    if (isMountedRef.current) {
      setLoading(false);
    }
  }, []);

  const checkStatus = useCallback(async () => {
    if (!checkoutId || (!rentalId && !extensionId)) {
      return;
    }

    try {
      if (isMountedRef.current) {
        setLoading(true);
        setError(null);
      }

      const variables = extensionId
        ? { checkoutId, rentalExtensionId: extensionId }
        : { checkoutId, rentalId };

      const result = await checkPaymentStatus({ variables });

      let paymentStatus;
      let errors;

      if (extensionId) {
        paymentStatus = result.data?.extensionGetPaymentStatus?.status;
        errors = result.data?.extensionGetPaymentStatus?.errors;
      } else if (isInstallment) {
        paymentStatus = result.data?.installmentGetPaymentStatus?.status;
        errors = result.data?.installmentGetPaymentStatus?.errors;
      } else {
        paymentStatus = result.data?.getPaymentStatus?.status;
        errors = result.data?.getPaymentStatus?.errors;
      }

      if (isMountedRef.current) {
        setStatus(paymentStatus);
      }

      if (onStatusChange) {
        onStatusChange(paymentStatus);
      }

      // Stop polling if payment is completed or failed
      if (paymentStatus === "paid" || paymentStatus === "failed") {
        stopPolling();

        if (paymentStatus === "failed" && errors?.length > 0) {
          const errorMessage = errors[0];
          if (isMountedRef.current) {
            setError(errorMessage);
          }
          if (onError) {
            onError(errorMessage);
          }
        }
      }
    } catch (err) {
      console.error("Payment status check error:", err);
      if (isMountedRef.current) {
        setError(err.message);
      }
      if (onError) {
        onError(err.message);
      }
      stopPolling();
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [
    checkoutId,
    rentalId,
    extensionId,
    isInstallment,
    checkPaymentStatus,
    onStatusChange,
    onError,
    stopPolling,
  ]);

  const startPolling = useCallback(() => {
    if (pollingRef.current) {
      stopPolling();
    }

    if (isMountedRef.current) {
      setPollingCount(0);
      setLoading(true);
    }

    // Check immediately
    checkStatus();

    // Then poll at intervals
    pollingRef.current = setInterval(() => {
      if (isMountedRef.current) {
        setPollingCount((prev) => {
          const newCount = prev + 1;

          if (newCount >= maxPollingAttempts) {
            stopPolling();
            if (isMountedRef.current) {
              setError("Payment status check timeout. Please check your payment manually.");
            }
            if (onError) {
              onError("Payment status check timeout");
            }
            return newCount;
          }

          checkStatus();
          return newCount;
        });
      }
    }, pollingInterval);
  }, [checkStatus, pollingInterval, maxPollingAttempts, stopPolling, onError]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      stopPolling();
    };
  }, [stopPolling]);

  // Manual status check
  const manualCheck = useCallback(async () => {
    await checkStatus();
  }, [checkStatus]);

  return {
    status,
    loading,
    error,
    pollingCount,
    startPolling,
    stopPolling,
    manualCheck,
    isPolling: !!pollingRef.current,
  };
};

export default usePaymentStatus;
