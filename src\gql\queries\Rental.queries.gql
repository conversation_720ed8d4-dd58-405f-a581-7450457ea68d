query GetBookingsQuery(
  $page: Int
  $limit: Int
  $from: String
  $customerName: String
  $bookingNo: String
  $to: String
  $cityName: [String!]
  $makeName: [String!]
  $allyName: String
  $status: [String!]
  $subStatus: [String!]
  $customerMobile: String
  $userId: ID
  $userNid: String
  $paymentMethod: String
  $paymentStatusFilter: [PaymentStatusFilter!]
  $orderBy: String
  $sortBy: String
  $pickUpDate: String
  $pickUpDateFrom: String
  $dropOffDate: String
  $dropOffDateTo: String
  $couponId: String
  $allyCompanyId: ID
  $airportId: ID
  $rentType: String
  $plateNo: String
  $paymentBrand: [PaymentBrand!]
  $agencyId: ID
) {
  dashboardRentals(
    page: $page
    limit: $limit
    allyName: $allyName
    bookingNo: $bookingNo
    cityName: $cityName
    customerName: $customerName
    from: $from
    makeName: $makeName
    status: $status
    subStatus: $subStatus
    to: $to
    customerMobile: $customerMobile
    userId: $userId
    userNid: $userNid
    paymentMethod: $paymentMethod
    paymentStatusFilter: $paymentStatusFilter
    orderBy: $orderBy
    sortBy: $sortBy
    pickUpDate: $pickUpDate
    pickUpDateFrom: $pickUpDateFrom
    dropOffDate: $dropOffDate
    dropOffDateTo: $dropOffDateTo
    couponId: $couponId
    allyCompanyId: $allyCompanyId
    airportId: $airportId
    rentType: $rentType
    plateNo: $plateNo
    paymentBrand: $paymentBrand
    agencyId: $agencyId
  ) {
    collection {
      id
      branchId
      carId
      paidAmount
      assignedBy
      assignedTo
      bookingNo
      userId
      isRentToOwn
      allyCompanyId
      couponDiscount
      totalBookingPrice
      branchName
      totalInsurancePrice
      pickUpDate
      dropOffDate
      enMakeName
      arMakeName
      arModelName
      dropOffTime
      pickUpTime
      enModelName
      carId
      branchId
      enVersionName
      arVersionName
      year
      withInstallment
      installmentsFullyPaid
      paidInstallmentsCount
      hasPendingExtensionRequests
      refundable
      isRentToOwn
      lastRentalDateExtensionRequest {
        id
        dropOffDate
        dropOffTime
        isPaid
        status
        requestNo
        extensionDays
        totalRemainingPrice
      }
      lastConfirmedExtensionRequest {
        dropOffDate
        dropOffTime
        numberOfDays
        totalBookingPrice
        totalInsurancePrice
      }
      rentalPayments {
        amount
        expireDate
        id
        paidTime
        paymentBrand
        paymentType
        refundedAmount
        status
      }
      pendingExtensionRequest {
        id
        dropOffDate
        dropOffTime
        isPaid
        status
        requestNo
        extensionDays
        totalRemainingPrice
      }
      isIntegratedRental
      enPickUpCityName
      arPickUpCityName
      enDropOffCityName
      arDropOffCityName
      status
      subStatus
      numberOfDays
      enAllyName
      arAllyName
      pricePerDay
      customerName
      createdAt
      hasPendingPaymentTransaction
      paymentMethod
      is24Passed
      isPaid
      refundedAt
      installments {
        id
        paymentStatus
        status
        hasPendingPaymentTransaction
      }
      mergedInstallments {
        id
        paymentStatus
        status
        hasPendingPaymentTransaction
      }
    }
    metadata {
      currentPage
      totalCount
    }
  }
}

query GetRentalDetailsQuery($id: ID!) {
  rentalDetails(id: $id) {
    residualAmount
    refundable
    assignedTo
    isPaid
    isRentToOwn
    addsPrice
    loyaltyMembership
    loyaltyPointRate
    loyaltyPointPrice
    loyaltyAppliedValueType
    loyaltyAppliedValue
    pendingPaymentOrder{
      id
    }
    loyaltyMaxAppliedValue
    loyaltyCollections {
      id
      numberOfPoints
      pointPrice
      status
    }
    loyaltyPointPrice
    loyaltyPointRate
    loyaltyType
    totalAddsPrice
    allyCompanyId
    allyRate
    allyRentalRate
    arAllyName
    arDropOffCityName
    arMakeName
    arModelName
    agencyId
    arPickUpCityName
    arVersionName
    bookingNo
    branchId
    dropOffBranchId
    handoverLat
    couponId
    handoverLng
    handoverPrice
    cancelledReason
    closingReason
    carId
    paymentBrand
    invoicedAt
    notes
    carImage
    customerMobile
    customerName
    customerProfileImage
    customerRate
    customerRentalRate
    dailyPrice
    couponId
    couponCode
    couponDiscount
    deliverAddress
    deliverType
    deliverLat
    deliverLng
    insuranceId
    deliveryPrice
    discountPercentage
    rentalIntegrationResponse
    hasPendingPaymentTransaction
    isIntegratedRental
    rentalIntegrationErrorMessage
    discountType
    discountValue
    dropOffCityId
    dropOffCityName
    dropOffDate
    dropOffTime
    enAllyName
    enDropOffCityName
    enMakeName
    rentalIntegrationStatus
    enModelName
    enPickUpCityName
    enVersionName
    id
    paidAmount
    insuranceIncluded
    invoicePic
    makeName
    modelName
    newGrandTotal
    numberOfDays
    paymentMethod
    pickUpCityId
    pickUpCityName
    pickUpDate
    pickUpTime
    priceBeforeDiscount
    priceBeforeInsurance
    priceBeforeTax
    pricePerDay
    refundedAmount
    refundedAt
    refundedBy
    status
    statusLocalized
    payable
    paymentLink {
      id
      token
      validForPayment
    }
    subStatus
    subStatusLocalized
    suggestedPrice
    taxValue
    totalOfTheRentToOwnPrice
    totalRtoInstallmentsAmount
    rtoPriceBeforeTax
    rtoTaxValue
    totalBookingPrice
    totalAmountDue
    totalInsurancePrice
    userId
    valueAddedTaxPercentage
    versionName
    year
    isUnlimited
    isUnlimitedFree
    unlimitedFeePerDay
    withInstallment
    installmentsFullyPaid
    paidInstallmentsCount
    totalUnlimitedFee
    remainingDueInstallmentsAmount
    completedInstallmentsAmount
    totalInsurancePrice
    insuranceName
    lastConfirmedExtensionRequest {
      dropOffDate
      dropOffTime
      numberOfDays
      totalBookingPrice
      totalInsurancePrice
    }
    rentalExtraServices {
      arDescription
      arSubtitle
      arTitle
      description
      enDescription
      enSubtitle
      enTitle
      extraServiceId
      extraServiceType
      iconUrl
      id
      isRequired
      payType
      rentalId
      serviceValue
      subtitle
      title
      totalServiceValue
    }
    integratedRentalContracts {
      contractNo
      createdAt
      operationDateTime
      operationType
      refundAmount
      rentalId
    }
    totalWalletPaidAmount
    hasPendingExtensionRequests
    ownCarDetails {
      rentalOwnCarPlan {
        finalInstallment
        firstInstallment
        firstPayment
        id
        monthlyInstallment
        noOfMonths
      }
    }
    pendingExtensionRequest {
      createdAt
      dropOffDate
      dropOffTime
      extensionDays
      id
      isPaid
      paymentMethod
      requestNo
      status
      statusLocalized
      totalRemainingPrice
    }
    rentalPayments {
      amount
      expireDate
      id
      paidTime
      paymentBrand
      paymentType
      refundedAmount
      status
    }
    hasPendingExtensionRequests
    paymentsAllGrandTotal
    paymentsTotalChargedAmounts
    paymentsTotalRemainingAmounts
    lastRentalDateExtensionRequest {
      id
      dropOffDate
      dropOffTime
      isPaid
      status
      requestNo
      extensionDays
      totalRemainingPrice
      statusLocalized
      paymentMethod
    }
    installments {
      loyaltyCollections {
        id
        numberOfPoints
        pointPrice
        status
      }
      hasPendingPaymentTransaction
      amount
      id
      installmentNumber
      paymentMethod
      status
      statusLocalized
      dueDate
      totalAmountPaidAt
      walletPaidAmount
      paymentStatus
      lastPaidInstallment {
        paymentBrand
      }
      canSendToAlly
      payable
      paymentLink {
        id
        token
        validForPayment
      }
    }
    mergedInstallments {
      loyaltyCollections {
        id
        numberOfPoints
        pointPrice
        status
      }
      hasPendingPaymentTransaction
      amount
      id
      installmentNumber
      paymentMethod
      status
      statusLocalized
      dueDate
      totalAmountPaidAt
      walletPaidAmount
      paymentStatus
      lastPaidInstallment {
        paymentBrand
      }
      canSendToAlly
      payable
      paymentLink {
        id
        token
        validForPayment
      }
    }
    walletTransactions {
      amount
    }
    rentalDateExtensionRequests {
      createdAt
      discount
      dropOffDate
      originalPricePerDay
      dropOffTime
      totalWalletPaidAmount
      canSendExtensionToAlly
      paymentBrand
      extensionDays
      pricePerDay
      packagePricePerDay
      actualPaymentMethod
      id
      isPaid
      paymentMethod
      isPaid
      requestNo
      status
      statusLocalized
      totalRemainingPrice
      refundable
      refundedAt
      withWallet
      walletTransactions {
        amount
        id
      }
      paymentStatus
      payable
      paymentLink {
        id
        token
        validForPayment
      }
    }

    allyRentalRejections {
      arAllyName
      arBranchName
      enBranchName
      enAllyName
      rejectedReason
      branchId
      allyCompanyId
      rejectedReasons {
        body
      }
      rejectedByName
      createdAt
    }
    rentalRejectedBaskets {
      arAllyName
      enAllyName
      rejectedReasons {
        arBody
        enBody
      }
    }
  }
}

query GetRentPrice(
  $carId: ID!
  $deliverLat: Float
  $isCarChanged: Boolean
  $deliverLng: Float
  # Can be no_delivery, one_way or two_ways
  $deliveryType: String
  $deliveryPrice: Float
  $handoverPrice: Float
  $handoverBranchPrice: Float
  $handoverBranch: ID
  $dropOffDate: String
  $dropOffTime: String
  $insuranceId: ID
  $pickUpDate: String
  $pickUpTime: String
  $allyExtraServices: [ID!]
  $branchExtraServices: [ID!]
  $couponId: ID
  $isUnlimited: Boolean
  $usedPrice: Float
  $withWallet: Boolean
  $payWithInstallments: Boolean
  $walletPaidAmount: Float
  $suggestedPrice: Float
  $rentalId: ID
  $ownCarPlanId: ID
  $isEdit: Boolean
  $paymentMethod: AboutPricePaymentMethod
) {
  aboutRentPrice(
    carId: $carId
    deliverLat: $deliverLat
    deliverLng: $deliverLng
    deliveryPrice: $deliveryPrice
    handoverPrice: $handoverPrice
    handoverBranchPrice: $handoverBranchPrice
    handoverBranch: $handoverBranch
    deliveryType: $deliveryType
    dropOffDate: $dropOffDate
    dropOffTime: $dropOffTime
    insuranceId: $insuranceId
    pickUpDate: $pickUpDate
    pickUpTime: $pickUpTime
    allyExtraServices: $allyExtraServices
    branchExtraServices: $branchExtraServices
    couponId: $couponId
    isUnlimited: $isUnlimited
    usedPrice: $usedPrice
    withWallet: $withWallet
    payWithInstallments: $payWithInstallments
    walletPaidAmount: $walletPaidAmount
    suggestedPrice: $suggestedPrice
    rentalId: $rentalId
    ownCarPlanId: $ownCarPlanId
    isEdit: $isEdit
    paymentMethod: $paymentMethod
    isCarChanged: $isCarChanged
  ) {
    addsPrice
    totalAddsPrice
    dailyPrice
    handoverPrice
    deliveryPrice
    discountPercentage
    discountType
    discountValue
    insuranceIncluded
    insuranceValue
    numberOfDays
    priceBeforeDiscount
    priceBeforeInsurance
    priceBeforeTax
    couponDiscount
    couponCode
    pricePerDay
    taxValue
    totalPrice
    totalAmountDue
    totalExtraServicesPrice
    valueAddedTaxPercentage
    isUnlimited
    isUnlimitedFree
    # insuranceName
    unlimitedFeePerDay
    totalUnlimitedFee
    totalOfTheRentToOwnPrice
    totalRtoInstallmentsAmount
    rtoPriceBeforeTax
    rtoTaxValue
    allyExtraServices {
      allyCompanyId
      arSubtitle
      enSubtitle

      extraServiceId
      id
      isActive
      isRequired
      payType
      serviceValue
      showFor
      subtitle
      totalServiceValue
    }
    totalInstallmentsAmount
    completedInstallmentsAmount
    remainingDueInstallmentsAmount
    couponErrorMessage
    remainingInstallmentsAmount
    rentToOwnInstallmentBreakdown {
      firstPayment
      monthlyInstallment
      finalInstallment
    }
    branchExtraServices {
      allyExtraServiceId
      arSubtitle
      branchId
      title
      arTitle
      enTitle
      enSubtitle
      id
      isActive
      isRequired
      payType
      serviceValue
      subtitle
      totalServiceValue
    }
    installmentsBreakdown {
      amount
      status
    }
    numberOfDays
  }
}

query RentalExtensionRequestPrice(
  $rentalId: ID!
  $dropOffDate: String!
  $dropOffTime: String!
  $paymentMethod: PaymentMethod
  $usedPrice: Float
) {
  rentalExtensionRequestPrice(
    rentalId: $rentalId
    dropOffDate: $dropOffDate
    dropOffTime: $dropOffTime
    paymentMethod: $paymentMethod
    usedPrice: $usedPrice
  ) {
    discount
    extensionDays
    isFixedExtend
    originalPricePerDay
    packagePricePerDay
    pricePerDay
    totalAmountDue
    totalBookingPrice
    totalRemainingPrice
    walletAmount
  }
}

query RentalAboutPrice(
  $id: ID!
  $PaymentBrand: RentalPaymentBrand
  $withInstallment: Boolean
  $withWallet: Boolean
) {
  rentalAboutPrice(
    id: $id
    paymentBrand: $PaymentBrand
    withInstallment: $withInstallment
    withWallet: $withWallet
  ) {
    addsPrice
    completedInstallmentsAmount
    couponCode
    couponDiscount
    couponErrorMessage
    couponId
    couponType
    dailyPrice
    deliveryPrice
    discountPercentage
    discountType
    discountValue
    handoverAntherCity
    handoverPrice
    totalExtensionsDays
    residualAmount
    installments {
      amount
      amountDue
      canSendToAlly
      createdAt
      dueDate
      hasPendingPaymentTransaction
      id
      installmentNumber
      isLastInstallment
      payable
      paymentMethod
      paymentStatus
      refundedAmount
      status
      statusLocalized
      totalAmountPaidAt
      totalPaidAmount
      updatedAt
      walletPaidAmount
      withWallet
    }
    insuranceIncluded
    isUnlimited
    isUnlimitedFree
    numberOfDays

    priceBeforeDiscount
    priceBeforeInsurance
    priceBeforeTax
    pricePerDay
    remainingDueInstallmentsAmount
    remainingInstallmentsAmount
    remainingInstallmentsDue
    rentalExtraServices {
      arDescription
      arSubtitle
      arTitle
      description
      enDescription
      enSubtitle
      enTitle
      extraServiceId
      extraServiceType
      iconUrl
      id
      isRequired
      payType
      rentalId
      serviceValue
      subtitle
      title
      totalServiceValue
    }
    rtoPriceBeforeTax
    rtoTaxValue
    taxValue
    totalAddsPrice
    totalAmountDue
    totalBeforeWallet
    totalExtensionsDays
    totalExtensionsPrice
    totalExtraServicesPrice
    totalInstallmentsAmount
    totalInsurancePrice
    totalOfTheRentToOwnPrice
    totalPrice
    totalRtoInstallmentsAmount
    totalUnlimitedFee
    totalWalletPaidAmount
    unlimitedFeePerDay
    valueAddedTaxPercentage
    walletAmount
  }
}
query GetRentalPaymentTransactions($id: ID) {
  rentalDetails(id: $id) {
    paymentTransactions {
      amount
      transactionDetails
      transactionNo
      type
    }
  }
}
