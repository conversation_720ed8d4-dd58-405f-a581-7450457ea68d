import { useMutation, useQuery } from "@apollo/client";
import { userCan } from "functions";

import React, { Fragment, useMemo, useState } from "react";
import swal from "sweetalert";
import { AssignRentalToMe } from "gql/mutations/AssignBookingToMe.gql";
import NotificationManager from "react-notifications/lib/NotificationManager";
import { FormattedMessage, useIntl } from "react-intl";
import { Button } from "@material-ui/core";
import { AssignRentalTo } from "gql/mutations/AssignRentalTo.gql";
import { Users } from "gql/queries/CustomerCare.gql";
import store from "../../../../store";

function useAssignBooking({ refetchBooking, inBookingDetails }) {
  const [assignRentalToMe] = useMutation(AssignRentalToMe);
  const [assignRentalTo] = useMutation(AssignRentalTo);
  const [BookingRecord, setBookingRecord] = useState({});
  const is_super_user = store?.getState()?.authUser?.user?.is_super_user;
  const user = store?.getState()?.authUser?.user;
  const isAgencyUser = user?.user_type?.includes("agency");

  const { formatMessage } = useIntl();
  const [OpenUsersModal, setOpenUsersModal] = useState(false);
  const { data: users } = useQuery(Users);
  const customerCare = useMemo(() => users?.users?.collection, [users]);

  const AssignUserToBooking = (record) => {
    if (userCan("rentals.assign")) {
      setBookingRecord(record);
      setOpenUsersModal(true);
    } else {
      swal({
        title: formatMessage({ id: "are.u.sure.?" }),
        text: formatMessage({ id: `u.want.to.Assign.this.Booking.to.u` }),
        icon: "warning",
        buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "button.yes" })],
        dangerMode: true,
      }).then((Yes) => {
        if (Yes) {
          assignRentalToMe({
            variables: {
              rentalId: +record.id,
            },
          }).then(() => {
            refetchBooking();
            NotificationManager.success(<FormattedMessage id="BookingAssignedSuccessfully" />);
          });
        }
      });
    }
  };
  const AssignBooking = (record) => (
    <div style={{ display: "flex", gap: "3px", height: "100%", alignItems: "center" }}>
      {(userCan("rentals.assign") &&
        (record?.status === "confirmed" || record?.status === "pending") &&
        !is_super_user &&
        !isAgencyUser) ||
      (is_super_user && !isAgencyUser) ? (
        !inBookingDetails ? (
          <Button
            variant="contained"
            color="primary"
            className="mx-smt-15 btn btn-assign mr-1 ml-1 pl-1 pr-1"
            style={{ fontSize: "12px" }}
            onClick={() => {
              AssignUserToBooking(record);
            }}
          >
            <FormattedMessage id="AssignTo" />
          </Button>
        ) : (
          <Button
            variant="contained"
            color="primary"
            size="small"
            className="btn-assign"
            style={{ minWidth: "auto", padding: "8px 12px" }}
            onClick={() => {
              AssignUserToBooking(record);
            }}
          >
            <FormattedMessage id="AssignTo" />
          </Button>
        )
      ) : (record?.status === "confirmed" || record?.status === "pending") && !isAgencyUser ? (
        <Button
          variant="contained"
          color="primary"
          className="mx-smt-15 btn btn-assign mr-1 ml-1"
          style={{ fontSize: "12px" }}
          onClick={() => {
            AssignUserToBooking(record);
          }}
        >
          <FormattedMessage id="AssignToMe" />
        </Button>
      ) : (
        <>
          {customerCare?.find((customer) => +customer.id === +record?.assignedTo)?.name ? (
            <div title="مسند الي">
              <FormattedMessage id="assigned_to" />{" "}
            </div>
          ) : null}
        </>
      )}

      {customerCare?.find((customer) => +customer.id === +record?.assignedTo)?.name}
    </div>
  );

  const AssignBookingBySuperUser = (_customerId) => {
    const customerId = _customerId || BookingRecord.assignedTo;
    const customerName = customerCare?.find((customer) => +customer.id === +customerId)?.name;
    if (!customerId) {
      NotificationManager.error(<FormattedMessage id="please.select.one.of.customercare" />);
      return;
    }
    swal({
      title: formatMessage({ id: "are.u.sure.?" }),
      text: `${formatMessage({ id: `u.want.to.Assign.this.Booking` })} ${customerName}`,
      icon: "warning",
      buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "button.yes" })],
      dangerMode: true,
    }).then((Yes) => {
      if (Yes) {
        assignRentalTo({
          variables: {
            rentalId: BookingRecord.id,
            userId: customerId,
          },
        }).then(() => {
          refetchBooking();
          NotificationManager.success(<FormattedMessage id="BookingAssignedSuccessfully" />);
          setOpenUsersModal(false);
        });
      }
    });
  };

  return {
    BookingRecord,
    AssignBooking,
    OpenUsersModal,
    setOpenUsersModal,
    AssignBookingBySuperUser,
    customerCare,
  };
}

export default useAssignBooking;
