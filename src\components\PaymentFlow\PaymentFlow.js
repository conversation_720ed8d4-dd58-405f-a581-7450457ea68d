/* eslint-disable prettier/prettier */
import React, { useState, useEffect, useRef } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON> } from "reactstrap";
import { CircularProgress } from "@material-ui/core";
import { useLazyQuery, useMutation } from "@apollo/client";
import { v4 as uuidv4 } from "uuid";
// import { NotificationManager } from "react-notifications";
import PaymentMethodSelection from "../PaymentMethodSelection/PaymentMethodSelection";
import PaymentStatusModal from "./PaymentStatusModal";
import TamaraWidget from "./TamaraWidget";
import usePaymentStatus from "./usePaymentStatus";
import useFirebase from "../../hooks/useFirebase";
import useTamaraEligibility from "../../hooks/useTamaraEligibility";
import {
  CHECKOUT_ID_QUERY,
  INSTALLMENT_CHECKOUT_ID_QUERY,
  EXTENSION_CHECKOUT_ID_QUERY,
  TAMARA_CREATE_CHECKOUT_MUTATION,
  TAMARA_CREATE_INSTALLMENT_CHECKOUT_MUTATION,
  TAMARA_CREATE_EXTENSION_CHECKOUT_MUTATION,
  PAY_BY_WALLET_MUTATION,
  EXTENSION_PAY_BY_WALLET_MUTATION,
} from "./graphql";
import "./PaymentFlow.scss";

const PaymentFlow = ({
  isOpen,
  onClose,
  rentalId,
  extensionId = null,
  isInstallment = false,
  totalAmount,
  walletBalance = 0,
  isAgencyDeactivated = false,
  onPaymentSuccess,
  onPaymentError,
}) => {
  const { formatMessage } = useIntl();
  const [currentStep, setCurrentStep] = useState("method_selection"); // method_selection, payment_form, status
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const [useWallet, setUseWallet] = useState(false);
  const [walletAmount, setWalletAmount] = useState(0);
  const [checkoutId, setCheckoutId] = useState(null);
  const [integrity, setIntegrity] = useState(null);
  const [tamaraCheckoutUrl, setTamaraCheckoutUrl] = useState(null);
  const [paymentError, setPaymentError] = useState(null);
  const [nonce] = useState(() => uuidv4());
  const paymentFormRef = useRef(null);
  // Firebase Remote Config for skip_integrity
  const { remoteConfigValues } = useFirebase();
  const { skip_integrity } = remoteConfigValues || {
    skip_integrity: { _value: "false" },
  };

  // Tamara eligibility check
  const { isPriceEligible: isTamaraEligible } = useTamaraEligibility(totalAmount);

  // Use the payment status hook
  const {
    status: paymentStatus,
    loading: statusLoading,
    error: statusError,
    startPolling,
    stopPolling,
    manualCheck,
  } = usePaymentStatus({
    checkoutId,
    rentalId,
    extensionId,
    isInstallment,
    onStatusChange: (status) => {
      if (status === "paid" && onPaymentSuccess) {
        onPaymentSuccess(selectedPaymentMethod);
      } else if (status === "failed" && onPaymentError) {
        onPaymentError(statusError || "Payment failed");
      }
    },
    onError: (error) => {
      setPaymentError(error);
      if (onPaymentError) {
        onPaymentError(error);
      }
    },
  });

  // Choose the appropriate mutations based on payment type
  const checkoutQuery = extensionId
    ? EXTENSION_CHECKOUT_ID_QUERY
    : isInstallment
    ? INSTALLMENT_CHECKOUT_ID_QUERY
    : CHECKOUT_ID_QUERY;

  const tamaraMutation = extensionId
    ? TAMARA_CREATE_EXTENSION_CHECKOUT_MUTATION
    : isInstallment
    ? TAMARA_CREATE_INSTALLMENT_CHECKOUT_MUTATION
    : TAMARA_CREATE_CHECKOUT_MUTATION;

  const walletMutation = extensionId ? EXTENSION_PAY_BY_WALLET_MUTATION : PAY_BY_WALLET_MUTATION;

  // GraphQL mutations and queries
  const [getCheckoutId, { loading: checkoutLoading }] = useLazyQuery(checkoutQuery, {
    errorPolicy: "all",
  });

  const [createTamaraCheckout, { loading: tamaraLoading }] = useMutation(tamaraMutation, {
    errorPolicy: "all",
  });

  const [payByWallet, { loading: walletLoading }] = useMutation(walletMutation, {
    errorPolicy: "all",
  });

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentStep("method_selection");
      setSelectedPaymentMethod(null);
      setUseWallet(false);
      setWalletAmount(0);
      setCheckoutId(null);
      setIntegrity(null);
      setTamaraCheckoutUrl(null);
      setPaymentError(null);
    } else {
      // Clean up payment widget script when modal closes
      cleanupPaymentWidget();
    }
  }, [isOpen]);

  const cleanupPaymentWidget = () => {
    const existingScript = document.getElementById("payment-widget");
    if (existingScript) {
      existingScript.remove();
    }
    const styleTag = document.getElementById("hyperpay-3ds-styles");
    if (styleTag) {
      styleTag.remove();
    }
  };

  const handlePaymentMethodSelect = async (paymentData) => {
    setSelectedPaymentMethod(paymentData.paymentMethod);
    setUseWallet(paymentData.useWallet);
    setWalletAmount(paymentData.walletAmount);

    try {
      if (paymentData.paymentMethod === "TAMARA") {
        await handleTamaraPayment(paymentData);
      } else if (paymentData.useWallet && paymentData.walletAmount >= totalAmount) {
        // Full wallet payment
        await handleWalletPayment();
      } else {
        // Regular payment with Hyperpay
        await handleHyperpayPayment(paymentData);
      }
    } catch (error) {
      setPaymentError(error.message);
      setCurrentStep("status");
    }
  };

  const handleTamaraPayment = async (paymentData) => {
    try {
      // Check Tamara eligibility before proceeding
      if (!isTamaraEligible) {
        throw new Error(
          formatMessage(
            { id: "Amount must be between {min} and {max} SAR" },
            {
              min: 0, // Will be replaced with actual limits in production
              max: 999999,
            },
          ),
        );
      }

      const variables = {
        locale: formatMessage.locale || "ar",
        requestPlatform: "dashboard",
        withWallet: paymentData.useWallet,
      };

      // Add the appropriate ID based on payment type
      if (extensionId) {
        variables.rentalExtensionId = extensionId;
      } else {
        variables.rentalId = rentalId;
      }

      const result = await createTamaraCheckout({ variables });

      console.log("Tamara checkout result:", result);

      // Check for different possible response structures
      let checkoutUrl = null;
      if (result.data?.tamaraCreateCheckoutSession?.checkoutUrl) {
        checkoutUrl = result.data.tamaraCreateCheckoutSession.checkoutUrl;
      } else if (result.data?.tamaraCreateInstallmentCheckoutSession?.checkoutUrl) {
        checkoutUrl = result.data.tamaraCreateInstallmentCheckoutSession.checkoutUrl;
      } else if (result.data?.tamaraCreateExtensionCheckoutSession?.checkoutUrl) {
        checkoutUrl = result.data.tamaraCreateExtensionCheckoutSession.checkoutUrl;
      }

      if (checkoutUrl) {
        console.log("Tamara checkout URL:", checkoutUrl);
        setTamaraCheckoutUrl(checkoutUrl);
        setCurrentStep("payment_form");
      } else {
        console.error("No checkout URL found in response:", result);
        // Check for errors in the response
        const errors =
          result.data?.tamaraCreateCheckoutSession?.errors ||
          result.data?.tamaraCreateInstallmentCheckoutSession?.errors ||
          result.data?.tamaraCreateExtensionCheckoutSession?.errors;

        if (errors && errors.length > 0) {
          throw new Error(errors[0]);
        } else {
          throw new Error(formatMessage({ id: "Failed to create Tamara checkout" }));
        }
      }
    } catch (error) {
      throw new Error(error.message || formatMessage({ id: "Tamara payment failed" }));
    }
  };

  const handleWalletPayment = async () => {
    try {
      const variables = extensionId ? { extensionRequestId: extensionId } : { rentalId };

      const result = await payByWallet({ variables });

      if (result.data?.payByWallet?.rental?.id) {
        setCurrentStep("status");
        if (onPaymentSuccess) {
          onPaymentSuccess("wallet");
        }
      } else {
        throw new Error(formatMessage({ id: "Wallet payment failed" }));
      }
    } catch (error) {
      throw new Error(error.message || formatMessage({ id: "Wallet payment failed" }));
    }
  };

  const handleHyperpayPayment = async (paymentData) => {
    try {
      const variables = {
        paymentBrand: paymentData.paymentMethod,
        withWallet: paymentData.useWallet,
      };

      // Add the appropriate ID based on payment type
      if (extensionId) {
        variables.rentalExtensionId = extensionId;
      } else {
        variables.rentalId = rentalId;
      }

      const result = await getCheckoutId({ variables });

      const checkoutData =
        result.data?.getCheckoutId ||
        result.data?.installmentGetCheckoutId ||
        result.data?.extensionGetCheckoutId;
      if (checkoutData?.checkoutId) {
        setCheckoutId(checkoutData.checkoutId);

        // Store payment context for payment result page
        sessionStorage.setItem(
          "paymentContext",
          JSON.stringify({
            rentalId,
            extensionId,
            isInstallment,
            checkoutId: checkoutData.checkoutId,
          }),
        );

        // Only set integrity if skip_integrity is false
        const shouldUseIntegrity = !skip_integrity?._value || skip_integrity._value === "false";
        const integrityValue = shouldUseIntegrity ? checkoutData.integrity : null;
        setIntegrity(integrityValue);

        setCurrentStep("payment_form");

        // Load Hyperpay widget
        setTimeout(() => {
          loadHyperpayWidget(checkoutData.checkoutId, integrityValue);
        }, 100);
      } else {
        throw new Error(formatMessage({ id: "Failed to get checkout ID" }));
      }
    } catch (error) {
      throw new Error(error.message || formatMessage({ id: "Payment initialization failed" }));
    }
  };

  const loadHyperpayWidget = (checkoutId, integrity) => {
    // Clean up any existing script
    cleanupPaymentWidget();

    // Create and load the payment widget script
    const script = document.createElement("script");
    script.id = "payment-widget";
    script.src = `${
      process.env.REACT_APP_OPPWA_URL || "https://eu-test.oppwa.com"
    }/v1/paymentWidgets.js?checkoutId=${checkoutId}`;
    script.async = true;
    script.setAttribute("crossorigin", "anonymous");
    script.setAttribute("nonce", nonce);

    // Only set integrity if skip_integrity is false and integrity is provided
    const shouldUseIntegrity = !skip_integrity?._value || skip_integrity._value === "false";
    if (integrity && shouldUseIntegrity) {
      script.setAttribute("integrity", integrity);
      console.log("Using integrity check for payment widget");
    } else {
      console.log(
        "Skipping integrity check for payment widget (skip_integrity:",
        skip_integrity?._value,
        ")",
      );
    }

    script.onload = () => {
      // Widget loaded successfully
      console.log("Payment widget loaded");
    };

    script.onerror = () => {
      setPaymentError(formatMessage({ id: "Failed to load payment widget" }));
      setCurrentStep("status");
    };

    document.body.appendChild(script);

    // Add custom styles for the payment form
    const style = document.createElement("style");
    style.id = "hyperpay-3ds-styles";
    style.textContent = `
      .wpwl-form {
        max-width: 100%;
        margin: 0 auto;
      }
      .wpwl-button {
        background-color: #007bff !important;
        border-color: #007bff !important;
        width: 100%;
        margin-top: 20px;
      }
      .wpwl-button:hover {
        background-color: #0056b3 !important;
        border-color: #0056b3 !important;
      }
    `;
    document.head.appendChild(style);
  };

  const handlePaymentSubmit = async () => {
    if (!checkoutId) return;

    try {
      setCurrentStep("status");
      // Start polling for payment status
      startPolling();
    } catch (error) {
      setPaymentError(error.message);
      if (onPaymentError) {
        onPaymentError(error.message);
      }
    }
  };

  const handleClose = () => {
    cleanupPaymentWidget();
    stopPolling();
    onClose();
  };

  const handleRetry = () => {
    setCurrentStep("method_selection");
    setPaymentError(null);
    stopPolling();
  };

  const isLoading = checkoutLoading || statusLoading || tamaraLoading || walletLoading;

  return (
    <>
      <Modal isOpen={isOpen} toggle={handleClose} size="lg" centered backdrop="static">
        <ModalHeader toggle={handleClose}>
          <FormattedMessage id="Payment" />
        </ModalHeader>

        <ModalBody>
          {currentStep === "method_selection" && (
            <PaymentMethodSelection
              isOpen
              onClose={handleClose}
              onPaymentMethodSelect={handlePaymentMethodSelect}
              loading={isLoading}
              walletBalance={walletBalance}
              totalAmount={totalAmount}
              isAgencyDeactivated={isAgencyDeactivated}
            />
          )}

          {currentStep === "payment_form" && (
            <div className="payment-form-container">
              <div className="payment-method-info mb-3">
                <h6>
                  <FormattedMessage id="Selected Payment Method" />:{" "}
                  <FormattedMessage id={selectedPaymentMethod} />
                </h6>
                {useWallet && walletAmount > 0 && (
                  <p className="text-muted">
                    <FormattedMessage
                      id="Using {amount} SAR from wallet"
                      values={{ amount: walletAmount.toFixed(2) }}
                    />
                  </p>
                )}
              </div>

              {selectedPaymentMethod === "TAMARA" && tamaraCheckoutUrl ? (
                <TamaraWidget
                  checkoutUrl={tamaraCheckoutUrl}
                  totalAmount={totalAmount}
                  locale={formatMessage.locale || "ar"}
                  onSuccess={() => {
                    setCurrentStep("status");
                    startPolling();
                  }}
                  onFailure={(error) => {
                    setPaymentError(error);
                    setCurrentStep("status");
                  }}
                  onCancel={() => {
                    setCurrentStep("method_selection");
                  }}
                />
              ) : (
                <div ref={paymentFormRef} className="hyperpay-form-container">
                  {checkoutId && (
                    <form
                      action={`${window.location.origin}/cw/dashboard/payment-result`}
                      className="paymentWidgets"
                      data-brands={
                        selectedPaymentMethod === "CREDIT_CARD"
                          ? "VISA MASTER"
                          : selectedPaymentMethod
                      }
                    />
                  )}
                </div>
              )}

              {isLoading && (
                <div className="text-center mt-3">
                  <CircularProgress size={30} />
                  <p className="mt-2">
                    <FormattedMessage id="Processing payment..." />
                  </p>
                </div>
              )}
            </div>
          )}

          {currentStep === "status" && (
            <PaymentStatusModal
              status={paymentStatus}
              error={paymentError || statusError}
              onRetry={handleRetry}
              onClose={handleClose}
              onManualCheck={manualCheck}
              loading={isLoading}
            />
          )}
        </ModalBody>

        {currentStep === "payment_form" && selectedPaymentMethod === "TAMARA" && (
          <ModalFooter>
            <Button color="secondary" onClick={handleClose} disabled={isLoading}>
              <FormattedMessage id="button.cancel" />
            </Button>
            <Button
              color="primary"
              onClick={handlePaymentSubmit}
              disabled={isLoading || !checkoutId}
            >
              {isLoading ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                <FormattedMessage id="Complete Payment" />
              )}
            </Button>
          </ModalFooter>
        )}
      </Modal>
    </>
  );
};

export default PaymentFlow;
