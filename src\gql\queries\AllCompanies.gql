query AllyCompanies(
  $isActive: Boolean
  $limit: Int
  $page: Int
  $pickupCityId: Int
  $dropoffCityId: Int
  $cityId: [Int!]
  $email: String
  $managerName: String
  $phoneNumber: String
  $allyClass: String
  $allyCompanyId: ID
  $allyCompanyIds:[ID!]
  $isRentToOwn: Boolean
  $pickStartDate: String
) {
  allyCompanies(
    isActive: $isActive
    limit: $limit
    page: $page
    pickupCityId: $pickupCityId
    dropoffCityId: $dropoffCityId
    cityId: $cityId
    email: $email
    managerName: $managerName
    phoneNumber: $phoneNumber
    allyClass: $allyClass
    allyCompanyId: $allyCompanyId
    allyCompanyIds:$allyCompanyIds
    isRentToOwn: $isRentToOwn
    pickStartDate: $pickStartDate
  ) {
    collection {
      email
      allyClass
      isActive
      addedBy
      phoneNumber
      managerName
      id
      logo
      arName
      enName
      isB2c
    }
    metadata {
      currentPage
      limitValue
      totalCount
      totalPages
    }
  }
}
query AvailableAllyCompanies(
  $isActive: Boolean
  $limit: Int
  $page: Int
  $pickupCityId: Int
  $dropoffCityId: Int
  $cityId: Int
  $email: String
  $managerName: String
  $phoneNumber: String
  $allyClass: String
  $allyCompanyId: ID
  $isRentToOwn: Boolean
  $canDelivery:Boolean
  $canHandover:Boolean 
  $pickStartDate: String
  $agencyId:ID
) {
  availableAllyCompanies(
    isActive: $isActive
    limit: $limit
    page: $page
    pickupCityId: $pickupCityId 
    dropoffCityId: $dropoffCityId
    cityId: $cityId
    email: $email
    managerName: $managerName
    phoneNumber: $phoneNumber
    allyClass: $allyClass
    allyCompanyId: $allyCompanyId
    isRentToOwn: $isRentToOwn
    pickStartDate: $pickStartDate
    canDelivery:$canDelivery
    canHandover:$canHandover
    agencyId:$agencyId
  ) {
    collection {
      email
      allyClass
      isActive
      addedBy
      phoneNumber
      managerName
      id
      allyHandoverCities{
        dropOffCityId
        pickUpCityId
        price
      }
      logo
      arName
      enName
      isB2c
      isExtendFixedPrice
    }
    metadata {
      currentPage
      limitValue
      totalCount
      totalPages
    }
  }
}
