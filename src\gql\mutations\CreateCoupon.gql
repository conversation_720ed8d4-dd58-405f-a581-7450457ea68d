
mutation CreateCoupons($allyCompanyIds:[ID!],$paymentMethod:CouponPaymentMethod,$branchIds:[ID!],$forNewCustomers:Boolean,$carVersionIds:[ID!],$minRentDays:Int,$minRentPrice:Int,$numOfDays:Int,$cityIds:[ID!],$code:String!,$discountType: Discount!,$discountValue: Float!,$expireAt: String,$maxLimitValue: Float,$numOfUsages:Int!,$numOfUsagesPerUser: Int!,$startAt: String!,
$paymentBrands:[CouponPaymentBrand!],
$agencyIds:[ID]

) {
  createCoupon(
    allyCompanyIds: $allyCompanyIds
    cityIds:$cityIds
    code: $code
    discountType: $discountType
    discountValue: $discountValue
    expireAt: $expireAt
    maxLimitValue:$maxLimitValue
    numOfUsages: $numOfUsages
    numOfUsagesPerUser: $numOfUsagesPerUser
    startAt: $startAt
    branchIds:$branchIds
    carVersionIds:$carVersionIds
    minRentDays:$minRentDays
    numOfDays:$numOfDays
    minRentPrice:$minRentPrice
    paymentMethod:$paymentMethod
    forNewCustomers:$forNewCustomers
    paymentBrands:$paymentBrands
    agencyIds:$agencyIds
  ) {
    coupon {
      allyCompanies{
        arName
        enName
      }
      areas{
        arName
        enName
      }
      code
      discountType
      discountValue
      expireAt
      id
      maxLimitValue
      numOfUsages
      numOfUsagesPerUser
      startAt
    }
    errors
    status
  }
}
