/* eslint-disable prettier/prettier */
/* eslint-disable eqeqeq */
/* eslint-disable react/prop-types */
import React from "react";
import PropTypes from "prop-types";
import { ListItem, CircularProgress } from "@material-ui/core";
import { FormattedMessage, useIntl } from "react-intl";

function BookingPriceSummary2({
  BookingPrice,
  calculatingPrice,
  insurance,
  handoverChecked,
  BookingDetails,
  isUnlimited,
  inBookingDetails,
  bookingType,
  plan,
  handoverToSamePickup,
  bookingId,
  change = false,
}) {
  const aboutPrice = BookingPrice?.aboutRentPrice || {};

  const {
    totalAddsPrice,
    deliveryPrice,
    discountPercentage,
    discountType,
    discountValue,
    insuranceIncluded,
    insuranceValue,
    numberOfDays,
    priceBeforeDiscount,
    priceBeforeInsurance,
    pricePerDay,
    dailyPrice,
    taxValue,
    totalPrice,
    valueAddedTaxPercentage,
    priceBeforeTax,
    handoverPrice,
    couponDiscount,
    couponCode,
    branchExtraServices,
    allyExtraServices,
    totalUnlimitedFee,
    totalAmountDue,
    remainingDueInstallmentsAmount,
    completedInstallmentsAmount,
    rentToOwnInstallmentBreakdown,
    insuranceName,
    addsPrice,
  } = aboutPrice;
  console.log(handoverPrice,"handoverPrice")
  const { locale } = useIntl();
  const totalBeforeInsurance = [
    bookingType == "rent-to-own"
      ? {
          msg: <FormattedMessage id="1st installment" />,
          value: (
            <FormattedMessage
              id="price.sr"
              values={{
                price: !change
                  ? BookingDetails?.rentalDetails?.ownCarDetails?.rentalOwnCarPlan?.firstInstallment
                  : rentToOwnInstallmentBreakdown?.firstPayment,
              }}
            />
          ),
        }
      : {
          msg: <FormattedMessage id="aboutPrice.PricePerDay" />,
          value: (
            <FormattedMessage
              id="price.sr"
              values={{ price: !change ? BookingDetails?.rentalDetails?.dailyPrice : dailyPrice }}
            />
          ),
        },
    bookingType == "rent-to-own"
      ? {
          msg: <FormattedMessage id="Monthly installment" />,
          value: (
            <FormattedMessage
              id="price.sr"
              values={{
                price: !change
                  ? BookingDetails?.rentalDetails?.ownCarDetails?.rentalOwnCarPlan
                      ?.monthlyInstallment
                  : rentToOwnInstallmentBreakdown?.monthlyInstallment,
              }}
            />
          ),
        }
      : null,
    bookingType == "rent-to-own"
      ? {
          msg: <FormattedMessage id="Final Installment" />,
          value: (
            <FormattedMessage
              id="price.sr"
              values={{
                price: !change
                  ? BookingDetails?.rentalDetails?.ownCarDetails?.rentalOwnCarPlan?.finalInstallment
                  : rentToOwnInstallmentBreakdown?.finalInstallment,
              }}
            />
          ),
        }
      : null,
    bookingType == "rent-to-own"
      ? {
          msg: <FormattedMessage id="No. of months" />,
          value: plan?.noOfMonths,
        }
      : {
          msg: (
            <FormattedMessage
              id="aboutPrice.totalDays"
              values={{
                days: `${!change ? BookingDetails?.rentalDetails?.numberOfDays : numberOfDays}`,
              }}
            />
          ),
          value: (
            <FormattedMessage
              id="price.sr"
              values={{
                price: !change
                  ? BookingDetails?.rentalDetails?.priceBeforeDiscount
                  : priceBeforeDiscount,
              }}
            />
          ),
        },
    BookingDetails?.rentalDetails?.discountValue || discountValue
      ? {
          msg: (
            <FormattedMessage
              id="aboutPrice.discount"
              values={{
                discount:
                  !change && BookingDetails?.rentalDetails?.discountType
                    ? BookingDetails?.rentalDetails?.discountType
                    : discountType
                    ? `${discountType}`
                    : null,
                percentage: `${
                  BookingDetails?.rentalDetails?.discountPercentage || discountPercentage
                }`,
              }}
            />
          ),
          value: (
            <FormattedMessage
              id="price.sr"
              values={{ price: BookingDetails?.rentalDetails?.discountValue || discountValue }}
            />
          ),
        }
      : null,
    !change
      ? BookingDetails?.rentalDetails?.couponCode
        ? {
            msg: <FormattedMessage id="code.label" />,
            value: BookingDetails?.rentalDetails?.couponCode,
          }
        : null
      : couponCode
      ? {
          msg: <FormattedMessage id="code.label" />,
          value: couponCode,
        }
      : null,
    !change
      ? BookingDetails?.rentalDetails?.couponCode
        ? {
            msg: <FormattedMessage id="couponDiscount" />,
            value: (
              <FormattedMessage
                id="price.sr"
                values={{ price: BookingDetails?.rentalDetails?.couponDiscount }}
              />
            ),
          }
        : null
      : couponCode
      ? {
          msg: <FormattedMessage id="couponDiscount" />,
          value: <FormattedMessage id="price.sr" values={{ price: couponDiscount }} />,
        }
      : null,

    {
      msg: "",
      value: (
        <FormattedMessage
          id="price.sr"
          values={{
            price: !change
              ? BookingDetails?.rentalDetails?.priceBeforeInsurance
              : priceBeforeInsurance,
          }}
        />
      ),
    },
  ];

console.log(allyExtraServices,"allyExtraServices")
  const extras = [
    !change
      ? BookingDetails?.rentalDetails?.insuranceIncluded
        ? {
            msg: (
              <FormattedMessage
                id="aboutPrice.insurance"
                values={{
                  insurance: BookingDetails?.rentalDetails?.insuranceName || insuranceName,
                }}
              />
            ),
            value: (
              <FormattedMessage
                id="price.sr"
                values={{ price: BookingDetails?.rentalDetails?.totalInsurancePrice }}
              />
            ),
          }
        : null
      : insuranceIncluded
      ? {
          msg: (
            <FormattedMessage
              id="aboutPrice.insurance"
              values={{ insurance: insurance.insuranceName }}
            />
          ),
          value: <FormattedMessage id="price.sr" values={{ price: insuranceValue }} />,
        }
      : null,
    !change
      ? BookingDetails?.rentalDetails?.rentalExtraServices?.length
        ? BookingDetails?.rentalDetails?.rentalExtraServices?.map((service) => ({
            msg: service[`${locale}Title`],
            value: (
              <FormattedMessage id="price.sr" values={{ price: service?.totalServiceValue }} />
            ),
          }))
        : null
      : branchExtraServices?.length
      ? branchExtraServices?.map((service) => ({
          msg: service[`${locale}Title`] || <FormattedMessage id="aboutPrice.extraServices" />,
          value: <FormattedMessage id="price.sr" values={{ price: service?.totalServiceValue }} />,
        }))
      : null,
      allyExtraServices?.length
      ? allyExtraServices?.map((service) => ({
          msg: service[`${locale}Title`] || <FormattedMessage id="aboutPrice.extraServices" />,
          value: <FormattedMessage id="price.sr" values={{ price: service?.totalServiceValue }} />,
        }))
      : null,
    isUnlimited
      ? {
          msg: <FormattedMessage id="Unlimited.KM" />,
          value:
            totalUnlimitedFee == 0 ? (
              <FormattedMessage id="free" />
            ) : change ? (
              <FormattedMessage
                id="price.sr"
                values={{
                  price: BookingDetails?.rentalDetails?.totalUnlimitedFee,
                }}
              />
            ) : (
              <FormattedMessage
                id="price.sr"
                values={{
                  price: totalUnlimitedFee || BookingDetails?.rentalDetails?.totalUnlimitedFee,
                }}
              />
            ),
        }
      : null,

    !BookingDetails?.rentalDetails?.deliveryPrice && !deliveryPrice
      ? {
          msg: "",
          value: (
            <FormattedMessage
              id="price.sr"
              values={{
                price:
                  BookingDetails?.rentalDetails?.totalAddsPrice ||
                  totalAddsPrice ||
                  BookingDetails?.rentalDetails?.addsPrice ||
                  addsPrice,
              }}
            />
          ),
        }
      : null,
  ];
  const carDeliveryService = [
    !change
      ? BookingDetails?.rentalDetails?.deliveryPrice
        ? {
            msg: <FormattedMessage id="aboutPrice.deliveryCost" />,
            value: (
              <FormattedMessage
                id="price.sr"
                values={{ price: BookingDetails?.rentalDetails?.deliveryPrice }}
              />
            ),
          }
        : null
      : deliveryPrice
      ? {
          msg: <FormattedMessage id="aboutPrice.deliveryCost" />,
          value: <FormattedMessage id="price.sr" values={{ price: deliveryPrice }} />,
        }
      : null,
    !change
      ? BookingDetails?.rentalDetails?.handoverPrice != 0 &&
        BookingDetails?.rentalDetails?.handoverPrice != null &&
        handoverChecked
        ? {
            msg: <FormattedMessage id="handoverprice" />,
            value: (
              <FormattedMessage
                id="price.sr"
                values={{ price: BookingDetails?.rentalDetails?.handoverPrice }}
              />
            ),
          }
        : null
      : handoverPrice != 0 && handoverPrice != null
      ? {
          msg: <FormattedMessage id={ handoverToSamePickup ? "return_to_same_Pickup_location": "handoverprice"} />,
          value: <FormattedMessage id="price.sr" values={{ price: handoverPrice }} />,
        }
      :null,
    {
      msg: "",
      value: (
        <FormattedMessage
          id="price.sr"
          values={{
            price:
              BookingDetails?.rentalDetails?.totalAddsPrice ||
              totalAddsPrice ||
              BookingDetails?.rentalDetails?.addsPrice ||
              addsPrice,
          }}
        />
      ),
    },
  ];

  const vatAndTotal = [
    !change
      ? BookingDetails?.rentalDetails?.valueAddedTaxPercentage
        ? {
            msg: (
              <FormattedMessage
                id="aboutPrice.total"
                values={{ vat: BookingDetails?.rentalDetails?.valueAddedTaxPercentage }}
              />
            ),
            value: (
              <FormattedMessage
                id="price.sr"
                values={{ price: BookingDetails?.rentalDetails?.priceBeforeTax }}
              />
            ),
          }
        : null
      : valueAddedTaxPercentage
      ? {
          msg: <FormattedMessage id="aboutPrice.total" values={{ vat: valueAddedTaxPercentage }} />,
          value: <FormattedMessage id="price.sr" values={{ price: priceBeforeTax }} />,
        }
      : null,
    !change
      ? BookingDetails?.rentalDetails?.valueAddedTaxPercentage
        ? {
            msg: (
              <FormattedMessage
                id="aboutPrice.vat"
                values={{
                  vat:
                    BookingDetails?.rentalDetails?.valueAddedTaxPercentage ||
                    valueAddedTaxPercentage,
                }}
              />
            ),
            value: (
              <FormattedMessage
                id="price.sr"
                values={{ price: BookingDetails?.rentalDetails?.taxValue }}
              />
            ),
          }
        : null
      : valueAddedTaxPercentage
      ? {
          msg: <FormattedMessage id="aboutPrice.vat" values={{ vat: valueAddedTaxPercentage }} />,
          value: <FormattedMessage id="price.sr" values={{ price: taxValue }} />,
        }
      : null,
    {
      msg:
        BookingDetails?.rentalDetails.walletTransactions ||
        (BookingDetails?.rentalDetails?.mergedInstallments &&
          BookingDetails?.rentalDetails?.mergedInstallments.length) ? (
          <>
            <FormattedMessage id="aboutPrice.grandTotal" />{" "}
            <FormattedMessage id="aboutPrice.+vat" />
          </>
        ) : (
          <FormattedMessage id="Due Amount" />
        ),
      value: (
        <FormattedMessage
          id="price.sr"
          values={{
            price: !change ? BookingDetails?.rentalDetails?.totalBookingPrice : totalPrice,
          }}
        />
      ),
    },
    BookingDetails?.rentalDetails?.mergedInstallments &&
    BookingDetails?.rentalDetails?.mergedInstallments.length
      ? {
          msg: (
            <>
              <FormattedMessage id="Completed.Payments" values={{ vat: valueAddedTaxPercentage }} />{" "}
              (
              {BookingDetails?.rentalDetails?.installments.filter(
                (installment) => installment.status == "paid" || installment.status == "",
              )?.length +
                "/" +
                BookingDetails?.rentalDetails?.installments?.length}
              )
            </>
          ),
          value: (
            <FormattedMessage
              id="price.sr"
              values={{
                price: !change
                  ? BookingDetails?.rentalDetails?.completedInstallmentsAmount
                  : completedInstallmentsAmount,
              }}
            />
          ),
        }
      : null,
    // Remaining Due

    BookingDetails?.rentalDetails.totalWalletPaidAmount
      ? {
          msg: (
            <>
              <FormattedMessage id="wallet" />
            </>
          ),
          value: (
            <FormattedMessage
              id="price.sr"
              values={{ price: BookingDetails?.rentalDetails.totalWalletPaidAmount }}
            />
          ),
        }
      : null,

    BookingDetails?.rentalDetails.walletTransactions
      ? {
          msg: (
            <>
              <FormattedMessage id="Due Amount" />
            </>
          ),
          value: (
            <FormattedMessage
              id="price.sr"
              values={{
                price: !change ? BookingDetails?.rentalDetails?.totalAmountDue : totalAmountDue,
              }}
            />
          ),
        }
      : null,

    BookingDetails?.rentalDetails?.mergedInstallments &&
    BookingDetails?.rentalDetails?.mergedInstallments.length
      ? {
          msg: (
            <>
              <FormattedMessage id="Remaining.Due" />
            </>
          ),
          value: (
            <FormattedMessage
              id="price.sr"
              values={{
                price: !change
                  ? BookingDetails?.rentalDetails?.remainingDueInstallmentsAmount
                  : remainingDueInstallmentsAmount,
              }}
            />
          ),
        }
      : null,
    BookingDetails?.rentalDetails?.refundedAmount
      ? {
          msg:
            locale == "en" ? (
              <>
                <span className="text-info">
                  {BookingDetails?.rentalDetails?.refundedAmount} SAR has been refunded to your
                  account
                </span>
              </>
            ) : (
              <span className="text-info">
                تم إيداع قيمة {BookingDetails?.rentalDetails?.refundedAmount} ريال لحساب بطاقتكم
              </span>
            ),
          value: "",
        }
      : null,
  ];
  function DataDisplay(i, row) {
    return (
      <ListItem
        key={JSON.stringify(i)}
        data-testid={`data-info-${i}`}
        className="d-flex justify-content-between align-items-center p-20"
      >
        {row?.msg ? <span>{row?.msg}</span> : <hr />}
        <span>{row?.value}</span>
      </ListItem>
    );
  }
  return BookingPrice || calculatingPrice || !change ? (
    <>
      {calculatingPrice && (
        <CircularProgress variant="determinate" size={40} thickness={4} value={100} />
      )}
      {!inBookingDetails && (
        <h3>
          <FormattedMessage id="aboutPrice" />
        </h3>
      )}

      <div className="alert-secondary">
        <h5>
          <FormattedMessage id="aboutPrice.Basic" />
        </h5>

        {totalBeforeInsurance.map((row, i) => row !== null && DataDisplay(i, row))}
        {totalAddsPrice > 0 ||
        BookingDetails?.rentalDetails?.totalAddsPrice ||
        BookingDetails?.rentalDetails?.addsPrice ||
        addsPrice ? (
          <>
            <h5>
              <FormattedMessage id="aboutPrice.extraServices" />
            </h5>
            {extras.length
              ? extras.flat(10)?.map((row, i) => row !== null && DataDisplay(i, row))
              : "null"}
          </>
        ) : null}
        {BookingDetails?.rentalDetails?.deliveryPrice || deliveryPrice || handoverChecked ? (
          <>
            <h5>
              <FormattedMessage id="Car_Delivery" />
            </h5>
            {carDeliveryService.map((row, i) => row !== null && DataDisplay(i, row))}
          </>
        ) : null}
        {
          <>
            {/* <h5>
          <FormattedMessage id="aboutPrice.total" /> سس
        </h5> */}
            {vatAndTotal.map((row, i) => row !== null && DataDisplay(i, row))}
          </>
        }
      </div>
    </>
  ) : null;
}

BookingPriceSummary2.propTypes = {
  BookingPrice: PropTypes.object,
  insurance: PropTypes.object,
  calculatingPrice: PropTypes.bool,
};

export default BookingPriceSummary2;
