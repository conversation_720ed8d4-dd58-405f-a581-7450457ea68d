/* eslint-disable prettier/prettier */
/**
 * Horizontal App
 */
import React, { Component } from "react";
import PropTypes from "prop-types";
import { Route, withRouter, Redirect } from "react-router-dom";

// app default layout
import RctCRMLayout from "../components/RctCRMLayout";

// router service
import routerService from "../services/_routerService";

class CRMLayout extends Component {
  render() {
    const { match, location } = this.props;
    const user_data = JSON.parse(localStorage.getItem("user_data"));
    if (location.pathname === "/cw") {
      return <Redirect to="/cw/dashboard" />;
    }
    if (location.pathname === "/cw/dashboard" && localStorage.getItem("user_data")) {
      return <Redirect to="/cw/dashboard/Statistics" />;
      // if (user_data && !user_data?.agency_id) {
      //   return <Redirect to="/cw/dashboard/bookings" />;
      // } else {
      //   // if(user_data?.user_type === "agency_customer_service") {
      //   // console.log(user_data);
      //   return <Redirect to="/cw/dashboard/customers" />;
      // }
    }
    return (
      <RctCRMLayout>
        {routerService &&
          routerService.map((route, key) => (
            <Route
              key={JSON.stringify(key)}
              path={`${match.url}/${route.path}`}
              component={route.component}
            />
          ))}
      </RctCRMLayout>
    );
  }
}

CRMLayout.propTypes = {
  match: PropTypes.object,
  location: PropTypes.object,
};

export default withRouter(CRMLayout);
