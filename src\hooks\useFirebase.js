// Firebase Remote Config hook for dashboard
import { useEffect, useMemo, useState } from "react";
import firebase from "firebase/app";
import "firebase/remote-config";

// Check if Firebase is already initialized
const getFirebaseApp = () => {
  if (firebase.apps.length === 0) {
    return null;
  }
  return firebase.app();
};

const firebaseConfig = process.env.REACT_APP_FIREBASE_CONFIG
  ? JSON.parse(process.env.REACT_APP_FIREBASE_CONFIG)
  : null;

function useFirebase() {
  const [remoteConfigValues, setRemoteConfigValues] = useState(null);
  const [isLoaded, setIsLoaded] = useState(false);

  // Initialize Firebase app if not already initialized
  const firebaseApp = useMemo(() => {
    if (!firebaseConfig) {
      console.warn("Firebase config not found");
      return null;
    }

    try {
      // Check if app is already initialized
      let app = getFirebaseApp();

      if (!app) {
        // Initialize Firebase app
        app = firebase.initializeApp(firebaseConfig);
        console.log("Firebase app initialized");
      }

      return app;
    } catch (error) {
      console.warn("Firebase initialization failed:", error);
      return null;
    }
  }, []);

  // Initialize Remote Config
  const remoteConfig = useMemo(() => {
    if (firebaseApp) {
      try {
        const config = firebaseApp.remoteConfig();
        // Set minimum fetch interval (30 seconds like web app)
        config.settings = {
          minimumFetchIntervalMillis: 30000,
        };
        return config;
      } catch (error) {
        console.warn("Remote Config initialization failed:", error);
        return null;
      }
    }
    return null;
  }, [firebaseApp]);

  // Fetch remote config values
  useEffect(() => {
    if (remoteConfig) {
      remoteConfig
        .fetchAndActivate()
        .then((activated) => {
          const values = remoteConfig.getAll();
          setRemoteConfigValues(values);
          setIsLoaded(true);
          console.log("Firebase Remote Config activated:", activated, values);
        })
        .catch((error) => {
          console.warn("Failed to fetch remote config:", error);
          // Set default values if fetch fails
          setRemoteConfigValues({
            skip_integrity: { _value: "false" },
            remove_tamara_limit: { _value: "false" },
            tamara_min_limit_value: { _value: "0" },
            tamara_max_limit_value: { _value: "999999" },
          });
          setIsLoaded(true);
        });
    } else {
      // Set default values if Firebase is not available
      setRemoteConfigValues({
        skip_integrity: { _value: "false" },
        remove_tamara_limit: { _value: "false" },
        tamara_min_limit_value: { _value: "0" },
        tamara_max_limit_value: { _value: "999999" },
      });
      setIsLoaded(true);
    }
  }, [remoteConfig]);

  // Return default values if Firebase is not available
  const defaultValues = {
    skip_integrity: { _value: "false" },
    remove_tamara_limit: { _value: "false" },
    tamara_min_limit_value: { _value: "0" },
    tamara_max_limit_value: { _value: "999999" },
  };

  return {
    remoteConfig,
    remoteConfigValues: remoteConfigValues || defaultValues,
    isLoaded,
  };
}

export default useFirebase;
