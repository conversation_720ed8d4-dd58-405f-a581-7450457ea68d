import { useMemo } from "react";
import useFirebase from "./useFirebase";

/**
 * Hook to check Tamara payment eligibility based on price limits from Firebase Remote Config
 * Matches the web app implementation for consistency
 */
const useTamaraEligibility = (totalAmount) => {
  const { remoteConfigValues } = useFirebase();

  // Extract Tamara configuration from Remote Config
  const tamaraConfig = useMemo(() => {
    const {
      remove_tamara_limit,
      tamara_min_limit_value,
      tamara_max_limit_value,
    } = remoteConfigValues || {};

    return {
      removeTamaraLimit: Boolean(remove_tamara_limit?._value === "true"),
      tamaraMinLimit: Number(tamara_min_limit_value?._value) || 0,
      tamaraMaxLimit: Number(tamara_max_limit_value?._value) || 999999,
    };
  }, [remoteConfigValues]);

  // Check if price is within Tamara limits
  const isPriceEligible = useMemo(() => {
    const { removeTamaraLimit, tamaraMinLimit, tamaraMaxLimit } = tamaraConfig;

    // If limits are removed, always eligible
    if (removeTamaraLimit) {
      return true;
    }

    // Check if amount is within range
    const amount = Number(totalAmount) || 0;
    return amount >= tamaraMinLimit && amount <= tamaraMaxLimit;
  }, [tamaraConfig, totalAmount]);

  // Get eligibility details for debugging/display
  const eligibilityDetails = useMemo(() => {
    const { removeTamaraLimit, tamaraMinLimit, tamaraMaxLimit } = tamaraConfig;
    const amount = Number(totalAmount) || 0;

    return {
      amount,
      minLimit: tamaraMinLimit,
      maxLimit: tamaraMaxLimit,
      limitsRemoved: removeTamaraLimit,
      isEligible: isPriceEligible,
      reason: !isPriceEligible
        ? amount < tamaraMinLimit
          ? `Amount ${amount} is below minimum limit ${tamaraMinLimit}`
          : `Amount ${amount} is above maximum limit ${tamaraMaxLimit}`
        : "Eligible for Tamara payment",
    };
  }, [tamaraConfig, totalAmount, isPriceEligible]);

  return {
    isPriceEligible,
    tamaraConfig,
    eligibilityDetails,
  };
};

export default useTamaraEligibility;
