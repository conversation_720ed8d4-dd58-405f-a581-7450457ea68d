{"[object Object]": "🐛", "missing": "🐞", "Arabic": "عربي", "English": "English", "app.copyRightText": "copyright Carwah {year}", "from": "From", "to": "To", "active": "Active", "inactive": "Inactive", "branch_ids": "Branch IDs", "area_ids": "Area IDs", "is_deep_link": "Is Deep Link", "vehicle_type_ids": "Vehicle Type IDs", "insurancesType": "Insurance Type", "noInsurance": "No Insurance", "car_version": "Car Version", "car_version_ids": "Car Version IDs", "car.availabilityStatus": "Car Availability Status", "icon": "Icon", "min_rent_days": "Min Rent Days", "num_of_days": " Number Of Days", "discount_value": "Discount Value", "num_of_usages_per_user": "num of usages per user", "num_of_usages": "num of usages", "code": "Code", "discount_type": "Discount Type", "min_rent_price": "min rent price ", "fixed_value": "Fixed Value", "name_ar": "name AR", "name_en": "name EN", "extra_service_ids": "Extra Service IDs", "daily_price_from": " Daily Price From", "daily_price_to": " Daily Price To", "options": "options", "available": "Available", "img_ar": "Image Ar", "img_en": "Image En", "created_by_id": "Created By ID", "updated_by_id": "Updated By ID", "Extension.id": "Extension ID", "male": "Male", "update price": "Update Price", "Extensions Total": "Extensions Total", "user.name": "User Name", "Creation date": "Creation date", "agenciesUsers": "User", "customers.list.customerStatus": "Customer Status", "Agency.Edit": "Edit Agency", "user.role": "Role", "Agency.Users": "Agency Users", "you want to deactivate this agency": "you want to deactivate this agency", "you want to activate this agency": "you want to activate this agency", "you want to deactivate this user": "you want to deactivate this user", "you want to activate this user": "you want to activate this user", "Deactivated successfully": "Deactivated successfully", "Activated Successfully": "Activated Successfully", "agency.details": "Agency Details", "fourteen.month": "Fourteen month", "fifteen.month": "Fifteen month", "thirteen.month": "Thirteen month", "Total paid amount": "Total paid amount", "Total remaining amount": "Total remaining amount", "Total charged amount": "Total charged amount", "sms": "SMS", "whatsapp": "Whatsapp", "Booking Total": "Booking Total", "Residual Amount": "Residual Amount", "Financial Transaction": "Financial Transaction", "transactionType": "Type", "seventeen.month": "Seventeen month", "eighteen.month": "Eighteen month", "nineteen.month": "Nineteen month", "twenty.month": "Twenty month", "sixteen.month": "Sixteen month", "twentyone.month": "Twenty one month", "twentytwo.month": "Twenty two.month", "twentythree.month": "Twenty three month", "applyed": "Applyed", "roleId": "Role Id", "RolesTimeLine": "Roles TimeLine", "CustomerTimeLine": "Customer Time<PERSON>ine", "StatusSucessfully": "Status changed  Sucessfully", "Distanceperday": "Distance per day", "status": "Status", "copylink": "Copy Delivery Location Link", "Daily price to must greater than Daily price from ": "Daily price to must greater than Daily price from ", "Daily price btw": "Daily price btw", "Daily price To": "Daily price To", "Daily price From": "Daily price From", "DeepLink": "DeepLink", "apply": "Apply", "add.season.success": "Add Season Successfully", "workingSeason": "Working Seasons", "Discount_coupon.label": "Discount Coupon", "Discount_coupon.placeholder": "Discount Coupon", "Invalid coupon": "Invalid coupon", "Create New Season": "Create New Season", "seasonName.label": "Season Name", "seasonName.placeholder": "Season Name", "Coupon_Code.label": "Coupon Code", "Coupon_Code.placeholder": "Coupon Code", "bookings.list.bookingStatus": "Booking Status", "apply.saeson.success": "Apply Saeson Success", "button.apply": "Apply", "female": "Female", "notes": "notes", "refund_with_discount": "Refund with Deduction", "citizen": "Citizen", "visitor": "Visitor", "24.hours": "24 Hours", "both": "Both", "online": "Online", "closed": "Closed", "sub_setFeedbacks": "SubsetFeedbacks", "oldData": "old Data", "newData": "new Data", "car_received": "Car Received", "car_id": "Car ID", "That doesn't look like a valid extension": "That doesn't look like a valid extension", "drop_off_city_id": "Drop Off City", "closingreason": "Closing Reason", "Editcoupon": "Edit Coupon", "dropoff-location": "Drop Off City", "not_collected": "Not-Collected", "pick_up_date": "Pick Up Date", "payment_method": "Payment Method", "drop_off_branch_id": "Drop Off Branch", "pick_up_time": "Pickup Time", "Payment.Brand": "Payment Brand", "acrissCode": "AcrissCode", "success.change.installment.setFeedbacks": "Installment setFeedbacks Changed Successfully", "u.want.to.refund.this.installment.to.u": " You Want To Refund This Installment To You", "Enter 4-digits code": "Enter 4-digits code", "Paid at": "Paid at", "send": "Send", "Wallet Amount": "Wallet Amount", "Resend code in": "Resend code in", "Mobile Number Updated Successfully": "Mobile Number Updated Successfully", "Edit Number": "Edit Mobile Number", "Wallet Value": "Wallet Value", "season.update.success": "Season Updated Successfully", "season.delete.success": "Season  Deleted Successfully", "drop_off_time": "Dropoff Time", "deliver_type": "Deliver Type", "paymentbrand": "paymentbrand", "Rental Packages": "Rental Packages", "due": "Due", "is_displayed": "Show", "ExtraServiceTimeline": "ExtraService Timeline", "is_active": "Is Active", "FeatureTimeline": "Feature Timeline", "ModelTimeLine": "Model TimeLine", "upcoming": "Upcoming", "for_new_customers": "For New Customers", "not_Collected": "Not Collected", "partially_refunded": "Partially Refunded", "CouponTimeLine": "Coupon TimeLine", "paid": "Paid", "overdue": "Over Due", "fully_refunded": "Fully Refunded", "not_collectable": "Not Collectable", "Remaining.Due": "Remaining Due", "Completed.Payments": "Completed Payments", "Decline Reason": "Decline Reason", "Date & Time": "Date & Time", "coupon.details": "Coupon Details", "rental.bookingSubStatus": " Booking SubStatus", "rental.bookingStatus": "Booking Status", "paymentstatus": "Payment Status", "User name": "User name", "drop_off_date": "dropoff Date", "Link Copied": "<PERSON>d", "rejectRent": "Reject Booking", "MakeTimeLine": "Make TimeLine", "assigned_to": "Assigned To", "MakeId": "Make ID", "CLOSED_AFTER_CONFIRM": "Closed After Confirm", "u.want.to.Assign.this.Booking": "You Want To Assign this Booking To", "u.want.to.Assign.this.Booking.to.u": "You Want To Assign This Booking To You ", "c.c.l": "Customer Care List ", "updatecars": "Update Cars", "please.selecte.reason": "Please Select Reason", "please.select.one.of.customercare": "Please Select From CustomerCare List ", "AssignToMe": "Assign To Me", "AssignTo": "Assign To", "assigned_by": "Assigned By", "Assign": "Assign", "assign": "Assign", "BookingAssignedSuccessfully": "Booking Assigned Successfully", "paymentmethod": "Payment Method", "sidebar.versions": "Car Versions", "rentals.per.setFeedbacks": "Rentals Per setFeedbacks", "users.per.month": "Users Per Month", "success.edit.manager": "Manager Edited Suuce<PERSON>fully", "note": "Note", "b.c.r": "Booking Close Reasons  ", "closeRental": "Close Booking ", "auto": "Automatic", "manager.details": "Manager Details", "CarThumbimage": "Car Thumb Image", "sidebar.addversion": "Add Version", "version.details": "Version Details ", "versionaddedsuccessfully": "Version Added Successfully", "setFeedbacksSucessfully": " setFeedbacks Changed successfully", "versiondeletedsuccessfully": "Version Deleted Successfully", "versioneditedsuccessfully": "Version Edited Successfully", "sidebar.editversion": "Edit Version", "inavailable": "Inavailable", "sidebar.app": "App", "sidebar.branches": "Branches", "district.label": "District Name", "district.placeholder": "District Name", "description.label": "description", "description.placeholder": "description", "branch": "branch", "sidebar.RoleDetails": "Role Details", "BranchName": "Branch Name", "sidebar.news": "News", "sidebar.horizontal": "Horizontal", "sidebar.horizontalMenu": "<PERSON><PERSON>u", "sidebar.general": "General", "sidebar.component": "Component", "sidebar.features": "Features", "success.edit.feature": "Feature Edited Successfully", "success.edit.rate": "Rate Edited Successfully", "sidebar.applications": "Applications", "sidebar.dashboard": "Dashboard", "sidebar.dashboardHome": "Dashboard", "UsersTimeLine": "Users TimeLine", "userId": "User ID", "sidebar.dashboard1": "Dashboard 1", "sidebar.dashboard2": "Dashboard 2", "sidebar.dashboard3": "Dashboard 3", "sidebar.modules": "<PERSON><PERSON><PERSON>", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "password": "Password", "national_id": "National Id", "passport_num": "Passport Number", "created_at": "Created At", "town": "Town", "is_ally_manager": "Is Ally Manager", "sidebar.agency": "Agency", "sidebar.pages": "Pages", "sidebar.gallery": "Gallery", "sidebar.pricing": "Pricing", "sidebar.terms&Conditions": "Terms & Conditions", "sidebar.feedback": "<PERSON><PERSON><PERSON>", "sidebar.report": "Report", "sidebar.faq(s)": "Faq(s)", "sidebar.advancedComponent": "Advance Component", "sidebar.blank": "Blank", "sidebar.session": "Session", "sidebar.login": "<PERSON><PERSON>", "sidebar.register": "Register", "sidebar.lockScreen": "Lock Screen", "sidebar.forgotPassword": "Forgot Password", "sidebar.404": "404", "sidebar.500": "500", "sidebar.uiComponents": "UI Components", "sidebar.alerts": "<PERSON><PERSON><PERSON>", "sidebar.appBar": "App Bar", "sidebar.avatars": "Avatars", "sidebar.buttons": "Buttons", "sidebar.bottomNavigations": "Bottom Navigations", "sidebar.badges": "Badges", "sidebar.cards": "Cards", "sidebar.cardsMasonry": "Cards Masonry", "sidebar.chip": "Chip", "sidebar.dialog": "Dialog", "sidebar.dividers": "Dividers", "sidebar.drawers": "Drawers", "sidebar.popover": "Popover", "sidebar.expansionPanel": "Expansion Panel", "sidebar.gridList": "Grid List", "sidebar.list": "List", "display_order": "display order", "sidebar.menu": "<PERSON><PERSON>", "customerId": "Customer ID", "is_barq": "Bar<PERSON>", "is_instant_confirmation": "Instant Confirmation ", "Price/ Day": "Price/ Day", "businessrequestsTimeLine": "Business Requests TimeLine", "sidebar.popoverAndToolTip": "Pop Over & ToolTip", "sidebar.progress": "Progress", "sidebar.snackbar": "Snackbar", "sidebar.selectionControls": "Selection Controls", "sidebar.advanceUiComponents": "Advance UI Components", "sidebar.dateAndTimePicker": "Date & Time Picker", "sidebar.tabs": "Tabs", "sidebar.stepper": "Stepper", "sidebar.notification": "Notification", "sidebar.sweetAlert": "<PERSON> Alert", "sidebar.autoComplete": "Auto Complete", "sidebar.aboutUs": "About Us", "sidebar.widgets": "Widgets", "sidebar.forms": "Forms", "sidebar.formElements": "Form Elements", "sidebar.textField": "Text Field", "sidebar.selectList": "Select List", "sidebar.charts": "Charts", "sidebar.reCharts": "Re Charts", "sidebar.reactChartjs2": "React Chartjs 2", "sidebar.icons": "Icons", "sidebar.themifyIcons": "Themify Icons", "sidebar.simpleLineIcons": "Simple Line Icons", "sidebar.materialIcons": "Material Icons", "sidebar.fontAwesome": "Font Awesome", "sidebar.tables": "Tables", "sidebar.basic": "Basic", "sidebar.dataTable": "Data Table", "sidebar.responsive": "Responsive", "sidebar.reactTable": "React Table", "sidebar.maps": "Maps", "sidebar.googleMaps": "Google Maps", "sidebar.leafletMaps": "Leaflet Maps", "sidebar.inbox": "Inbox", "sidebar.users": "Users", "sidebar.userProfile1": "User Profile 1", "sidebar.userProfile2": "User Profile 2", "sidebar.userManagement": "User Management", "sidebar.userProfile": "User Profile", "sidebar.userList": "User List", "sidebar.calendar": "Calendar", "wallet__history": "History", "Wallet.payment": "Wallet", "sidebar.cultures": "Cultures", "sidebar.dnd": "Dnd", "user.name.placeholder": "User Name", "sidebar.selectable": "Selectable", "sidebar.customRendering": "Custom Rendering", "sidebar.chat": "Cha<PERSON>", "sidebar.toDo": "ToDo", "sidebar.editor": "Editor", "sidebar.wysiwygEditor": "WYSIWYG Editor", "Discount Rate": "Discount Rate", "Original Price/ Day": "Original Price/ Day", "Recommended Price/Day": "Recommended Price/Day", "Due Value": "Due Value", "sidebar.quillEditor": "Quill Editor", "sidebar.reactAce": "React Ace", "sidebar.dragAndDrop": "Drag And Drop", "sidebar.reactDragula": "React <PERSON>", "sidebar.reactDnd": "React Dnd", "Recalculate": "Recalculate", "sidebar.blogManagement": "Blog Management", "sidebar.ecommerce": "Ecommerce", "sidebar.shopList": "Shop List", "sidebar.shopGrid": "Shop Grid", "sidebar.invoice": "Invoice", "RATED": "Rated", "sidebar.multilevel": "Multilevel", "sidebar.sublevel": "Sublevel", "sidebar.crypto": "Crypto", "sidebar.marketCap": "Market cap", "sidebar.wallet": "Wallet", "sidebar.trade": "trade", "sidebar.bookings": "Bookings", "sidebar.customers": "Customers", "sidebar.customersList": "Customers List", "agency.name": "Agency Name", "sidebar.rentalDetails": "Booking Details", "agency.logo": "Agency Logo", "agency.id": "Agency ID", "sidebar.changesetFeedbacks": "Change setFeedbacks", "under_review": "Under Review", "agencies": "Agencies", "change.status": "Change Status", "sidebar.alliesRates": "Allies Rates", "user": "User", "resend_api_request": "Resend to ally", "is_api_integrated": "Is Api Integrated", "commercial_regestration": "Commercial Regestration", "is_extend_fixed_price": "Is Extend Fixed Price", "is_b2c": "Is B2c", "app": "App", "timeline": "Timeline", "verify_yakeen": "Yakeen verification", "reason": "Reason", "feedbacks": "Help & Support", "manage.ticket": "Manage Ticket", "loyalty_settings": "Loyalty Points", "status changed successfully": "Status Changed Successfully", "Ticket.booking": "Booking No. / ID", "CarwahReply": "CarwahReply", "refund_request": "Refund Request ", "customer.placeholder": "Customer/Phone No.", "feedback": "<PERSON><PERSON><PERSON>", "Repliedby": "Replied by", "Topic": "Topic", "completed": "Completed", "Resolving.date": "Resolving date", "Customer.ID": "Customer ID", "IBAN": "IBAN", "Full name of account": "Full name of the account holder", "sidebar.support": "Help & Support", "Bank.Name": "Bank Name", "Description": "Description", "reply": "Reply", "sidebar.Support": "Help & Support", "Reporting date": "Reporting date", "Resolving date": "Resolving date", "sidebar.cw": "Carwah", "widgets.totalEarns": "Total Earns", "widgets.emailsStatistics": "Emails Statistics", "widgets.totalRevenue": "Total Revenue", "widgets.onlineVistors": "Online Vistors", "widgets.trafficSources": "Traffic Sources", "widgets.RecentOrders": "Recent Orders", "widgets.topSellings": "Top Sellings", "widgets.productReports": "Product Reports", "widgets.productStats": "Product Stats", "widgets.ComposeEmail": "Compose Email", "widgets.employeePayroll": "Employee Payroll", "widgets.visitors": "Visitors", "widgets.orders": "Orders", "widgets.ordersetFeedbacks": "Order setFeedbacks", "widgets.totalSales": "Total Sales", "widgets.netProfit": "Net Profit", "widgets.overallTrafficsetFeedbacks": "Overall Traffic setFeedbacks", "widgets.tax": "Tax", "widgets.expenses": "Expenses", "widgets.currentTime": "Current Time", "widgets.currentDate": "Current Date", "widgets.todayOrders": "Today Orders", "widgets.toDoList": "To Do Lists", "widgets.discoverPeople": "Discover People", "widgets.commments": "Comments", "widgets.newCustomers": "New Customers", "widgets.recentNotifications": "Recent Notifications", "widgets.appNotifications": "App Notifications", "widgets.ratings": "Ratings", "widgets.newEmails": "New Emails", "widgets.siteVisitors": "Site Visitors", "widgets.socialCompanines": "Social Companines", "widgets.recentActivities": "Recent Activities", "widgets.recentOrders": "Recent Orders", "widgets.gallery": "Gallery", "widgets.pricing": "Pricing", "No.branches.found": "No Branches Found", "widgets.enterpriseEdition": "Enterprise Edition", "widgets.personalEdition": "Personal Edition", "No.model.found": "no modal found ", "widgets.teamEdition": "Team Edition", "widgets.standard": "Standard", "widgets.advanced": "Advanced", "carcount": "Car Count", "widgets.master": "Master", "widgets.Mega": "Mega", "widgets.logIn": "Log In", "widgets.signUp": "Sign Up", "widgets.lockScreen": "Lock Screen", "widgets.alertsWithLink": "<PERSON><PERSON><PERSON> With Link", "widgets.additionalContent": "Additional Content", "widgets.alertDismiss": "<PERSON><PERSON>", "widgets.uncontrolledDisableAlerts": "Uncontrolled Disable Alerts", "widgets.contexualAlerts": "Contexual <PERSON>", "widgets.alertsWithIcons": "Alerts With Icons", "widgets.Simple App Bars": "Simple App Bars", "widgets.appBarsWithButtons": "App Bars With Buttons", "widgets.imageAvatars": "Image Avatars", "widgets.lettersAvatars": "Letters Avatars", "widgets.iconsAvatars": "Icons Avatars", "widgets.flatButtons": "Flat Buttons", "widgets.raisedButton": "Raised <PERSON>", "widgets.buttonWithIconAndLabel": "Button With Icon And Label", "widgets.floatingActionButtons": "Floating Action Buttons", "widgets.iconButton": "Icon <PERSON>", "widgets.socialMediaButton": "Social Media Button", "widgets.reactButton": "React <PERSON>", "widgets.buttonOutline": "Button Outline", "widgets.buttonSize": "<PERSON><PERSON>", "widgets.buttonState": "Button State", "widgets.buttonNavigationWithNoLabel": "button Navigation With No Label", "widgets.buttonNavigation": "Button Navigation", "widgets.iconNavigation": "Icon Navigation", "widgets.badgeWithHeadings": "Badge With Headings", "widgets.contexualVariations": "Contexual Variations", "widgets.badgeLinks": "Badge Links", "widgets.materialBadge": "Material Badge", "widgets.simpleCards": "Simple Cards", "widgets.backgroundVarient": "Background Varient", "widgets.cardOutline": "Card Outline", "widgets.overlayCard": "Overlay Card", "widgets.cardGroup": "Card Group", "widgets.cardTitle": "Card Title", "widgets.speacialTitleTreatment": "Speacial Title Treatment", "widgets.chipWithClickEvent": "<PERSON> With Click Event", "widgets.chipArray": "<PERSON>", "widgets.dialogs": "Dialogs", "widgets.listDividers": "List Dividers", "widgets.insetDividers": "Inset Dividers", "widgets.temporaryDrawers": "Temporary Drawers", "widgets.permanentDrawers": "Permanent Drawers", "widgets.simpleExpansionPanel": "Simple Expansion Panel", "widgets.controlledAccordion": "Controlled Accordion", "widgets.secondaryHeadingAndColumns": "Secondary Heading And Columns", "widgets.imageOnlyGridLists": "Image Only Grid Lists", "widgets.advancedGridLists": "Advanced Grid Lists", "widgets.singleLineGridLists": "Single Line Grid Lists", "widgets.simpleLists": "Simple Lists", "widgets.folderLists": "Folder Lists", "widgets.listItemWithImage": "List Item With Image", "widgets.switchLists": "Switch Lists", "widgets.insetLists": "Inset Lists", "widgets.nestedLists": "Nested Lists", "widgets.checkboxListControl": "Checkbox List Control", "widgets.pinedSubHeader": "Pined Sub Header", "widgets.InteractiveLists": "Interactive Lists", "widgets.simpleMenus": "Simple Menus", "widgets.selectedMenu": "Selected Menu", "widgets.maxHeightMenu": "Max Height Menu", "widgets.changeTransition": "Change Transition", "widgets.paper": "Paper", "widgets.anchorPlayGround": "Anchor Play Ground", "widgets.tooltip": "ToolTip", "widgets.positionedToolTips": "Positioned Snackbar", "widgets.circularProgressBottomStart": "Circular Progress Bottom Start", "widgets.interactiveIntegration": "Interactive Integration", "widgets.determinate": "Determinate", "widgets.linearProgressLineBar": "Linear Progress Line Bar", "widgets.indeterminate": "Indeterminate", "widgets.buffer": "<PERSON><PERSON><PERSON>", "widgets.query": "Query", "widgets.transitionControlDirection": "Transition Control Direction", "widgets.simpleSnackbar": "Simple Snackbar", "widgets.positionedSnackbar": "Positioned Snackbar", "widgets.contexualColoredSnackbars": "Contexual Colored Snackbars", "widgets.simpleCheckbox": "Simple Checkbox", "widgets.interminateSelection": "Interminate Selection", "widgets.disabledCheckbox": "Disabled Checkbox", "widgets.customColorCheckbox": "Custom Color Checkbox", "widgets.VerticalStyleCheckbox": "Vertical Style Checkbox", "widgets.horizontalStyleCheckbox": "Horizontal Style Checkbox", "widgets.radioButtons": "Radio Buttons", "widgets.disabledRadio": "Disabled Radio", "widgets.withError": "With Error", "widgets.switches": "Swiches", "widgets.dateAndTimePicker": "Date And Time Picker", "widgets.defaultPicker": "<PERSON><PERSON><PERSON>", "widgets.timePicker": "Time Picker", "widgets.weekPicker": "Week Picker", "widgets.defaultDatePicker": "Default Date Picker", "widgets.customPicker": "Custom Picker", "widgets.tabs": "Tabs", "widgets.fixedTabs": "Fixed Tabs", "widgets.basicTab": "Basic Tab", "widgets.wrappedLabels": "Wrapped Labels", "widgets.centeredLabels": "Centered Labels", "widgets.forcedScrolledButtons": "Forced Scrolled <PERSON><PERSON>", "widgets.iconsTabs": "Icons Tabs", "widgets.withDisableTabs": "With Disable Tabs", "widgets.iconWithLabel": "Icon With Label", "widgets.stepper": "Stepper", "widgets.horizontalLinear": "Horizontal Linear", "widgets.horizontalNonLinear": "Horizontal Non Linear", "widgets.horizontalLinerAlternativeLabel": "Horizontal Liner Alternative Label", "widgets.horizontalNonLinerAlternativeLabel": "Horizontal Non Liner Alternative Label", "loyalty partners": "loyalty partners", "Earning Value": "Earning Value", "Miles Price": "<PERSON>", "Loyalty Partner": "Loyalty Partner", "widgets.verticalStepper": "Vertical Stepper", "AlFursan": "<PERSON><PERSON><PERSON><PERSON>", "Membership ID not found in Al Fursan": "Membership ID not found in Al Fursan", "sar": "SAR", "ID mustn't start with 0, and must consist of 8 or 10 digits": "ID mustn't start with 0, and must consist of 8 or 10 digits", "percentage.label": "percentage", "widgets.descriptionAlert": "Description Alert", "widgets.customIconAlert": "Custom Icon Alert", "widgets.withHtmlAlert": "With Html Alert", "widgets.promptAlert": "Prompt Alert", "widgets.passwordPromptAlert": "Password Prompt Al<PERSON>", "widgets.customStyleAlert": "Custom Style Alert", "widgets.autoComplete": "Auto Complete", "widgets.reactSelect": "React Select", "widgets.downshiftAutoComplete": "Downshift Auto Complete", "widgets.reactAutoSuggests": "React Auto Suggests", "widgets.aboutUs": "About Us", "widgets.ourVission": "Our Vission", "widgets.ourMissions": "Our Missions", "widgets.ourMotivation": "Our Motivation", "widgets.defualtReactForm": "Defualt React Form", "widgets.url": "Url", "widgets.textArea": "Text Area", "widgets.file": "File", "widgets.formGrid": "Form Grid", "widgets.inlineForm": "Inline Form", "widgets.inputSizing": "Input Sizing", "widgets.inputGridSizing": "Input Grid Sizing", "widgets.hiddenLabels": "Hidden Labels", "widgets.formValidation": "Form Validation", "widgets.number": "Number", "widgets.date": "Date", "widgets.time": "Time", "widgets.color": "Color", "widgets.search": "Search", "widgets.selectMultiple": "Select Multiple", "widgets.inputWithSuccess": "Input With Success", "widgets.inputWithDanger": "Input With Danger", "widgets.simpleTextField": "Simple Text Field", "widgets.componet": "Components", "widgets.layouts": "Layouts", "widgets.inputAdorements": "Input Adorements", "widgets.formattedInputs": "Formatted Inputs", "widgets.simpleSelect": "Simple Select", "widgets.nativeSelect": "Native Select", "widgets.MutltiSelectList": "Mutlti Select List", "widgets.lineChart": "Line Chart", "widgets.barChart": "Bar Chart", "widgets.stackedBarChart": "Stacked Bar Chart", "widgets.lineBarAreaChart": "Line Bar Area Chart", "widgets.areaChart": "Area Chart", "widgets.stackedAreaChart": "Stacked Area Chart", "widgets.verticalChart": "Vertical Chart", "widgets.radarChart": "Radar Chart", "widgets.doughnut": "Doughnut", "widgets.polarChart": "Polar Chart", "widgets.pieChart": "Pie Chart", "widgets.bubbleChart": "Bubble Chart", "widgets.horizontalBar": "Horizontal Bar", "widgets.basicTable": "Basic Table", "widgets.contexualColoredTable": "Contexual Colored Table", "widgets.dataTable": "Data Table", "widgets.employeeList": "Employee List", "widgets.responsiveTable": "Responsive Table", "widgets.responsiveFlipTable": "Responsive Flip Table", "widgets.reactGridControlledStateMode": "React Grid Controlled State Mode", "widgets.productsReports": "Products Reports", "widgets.taskList": "Task List", "widgets.basicCalender": "Basic Calender", "widgets.culturesCalender": "Cultures Calender", "widgets.dragAndDropCalender": "Drag And Drop Calender", "widgets.selectableCalender": "Selectable Calender", "widgets.customRendering": "Custom Rendering", "widgets.customCalender": "Custom Calender", "widgets.searchMailList": "Search Mail List", "languages": "Language Selection", "add.something": "Add {something}", "create.something": "Create {something}", "create.new.something": "Create new {something}", "create": "create", "youmustselectrole": "you must select role", "ally_vacations": "ally vacations", "car_models": "car models", "list": "list", "carType": "Car Type", "sidebar.roles": "Roles", "Edit": "Edit", "1Mile.Rate": "1 Mile Rate", "Max. value is 100": "Max. value is 100", "Maximumlimitvalue.placeholder": "Maximum limit value", "Invalid ID": "Invalid ID", "Alfursan": "<PERSON><PERSON><PERSON><PERSON>", "MembershipID": "Membership ID", "Maximumlimitvalue.label": "Maximum limit value", "sidebar.LoyaltyPoints": "Loyalty Partners", "Verified successfully": "Verified successfully", "MilePrice": "MilePrice", "ActiveOn": "Active On", "miles": "<PERSON>", "alfursan": "<PERSON><PERSON><PERSON><PERSON>", "sent": "<PERSON><PERSON>", "failed": "Failed", "in_progress": "in Progress", "1 Mile Rate": "1 Mile Rate", "Applied Value": "Applied Value", "Mile Price": "Mile Price", "AppliedValue": "Applied Value", "RoleAddSuccessfully": "Role Add Successfully", "Fixedvalue.label": "Fixed value", "sidebar.addRole": "Add Role", "car_features": "car features", "vehicle_types": "vehicle types", "car_versions": "car versions", "branches": "branches", "users": "users", "user.details": "User Details", "update_setFeedbacks": "update_setFeedbacks", "update": "update", "activation": "activation", "arabicdescription": "Arabic  role Description ", "englishrolename": "English role name ", "englishrolename.placeholder": "English role name ", "englishroledescription": "English role Description", "view": "view", "show": "show", "CREDIT_CARD": "CREDIT CARD", "APPLEPAY": "APPLE PAY", "booking.invociedAt": "InvoicedAt", "arabicnamerole": "arabic role name", "arabicnamerole.placeholder": "arabic role name", "rentals": "bookings", "privileges": "privileges", "delete": "delete", "name": "Name", "rental": "rental", "save": "save", "role": "role", "cancel": "Cancel", "booking": "booking", "customer.data": "Customer Data", "components.buyNow": "Buy Now", "components.choose": "<PERSON><PERSON>", "components.username": "Username", "components.passwords": "Passwords", "widgets.forgetPassword": "Forget Password", "success.create.make": "A new make has been added successfully", "success.create.rental": "A new rent has been added successfully", "success.create.extensionRequest": "A new extension request has been added successfully", "success.update.extensionRequest": "The extension request has been updated successfully", "success.edit.rental": "<PERSON><PERSON> has been edited successfully", "components.signIn": "Sign In", "components.perpage.limit": "Show", "isAllyManager": "Is Ally Manager", "Coupon": "Coupon", "CouponType": "Coupon Type", "code.label": "Coupon Code", "code.placeholder": "Coupon Code", "numOfUsages.label": "No. of total usage", "numOfUsages.placeholder": "No. of total usage", "numOfUsagesPerUser.label": "No. of usage per user", "numOfUsagesPerUser.placeholder": "No. of usage per user", "discountValue.label": "Discount value", "discountValue.placeholder": "Discount value", "Addcoupon": "Create Coupon", "isBarq": "Bar<PERSON>", "isInstantConfirmation": "Instant Confirmation", "percentage": "Percentage ", "fixedvalue": "Fixed value", "image": "Image", "versionId": "version ID ", "expire_at": "Expire At", "start_at": "Start At ", "payment_brands": "Payment Brands", "pick_up_datetime": "Pickup Datetime", "car_model_id": "Car Model ID", "car_image": "car image", "additional_notes": "additional notes", "number_of_cars": "number of cars ", "number_of_months": "number of months", "CompanyTimeLine": "Company TimeLine", "ally_rate_id": "ally rate id", "other_car_name": "other car name", "acriss_type_id": "Acriss type ID", "ally_company_id": "ally company id", "delivery_grace_time": "delivery grace time", "can_handover": "can handover", "max_limit_value": "max Limit Value", "maxLimitValue.label": "Maximum limit value", "maxLimitValue.placeholder": "Maximum limit value", "ally_company_ids": "Ally company IDs", "100.max": "the value must not exceed 100", "maxout.99": "Max. is 999999.99", "partially_blocked": "Partially blocked", "customer.setFeedbacks": "customer setFeedbacks", "start.date": "Start date", "blocked": "Blocked ", "type": "Type", "end.date": "End date", "common.Statistics": "Statistics", "Enddate": "End date", "Startdate": "Start date", "sidebar.coupons": "Coupons", "coupons": "Coupons", "components.dontHaveAccountSignUp": "Dont Have Account SignUp", "components.enterUserName": "Enter User Name", "components.enterEmailAddress": "Enter Email Address", "components.confirmPasswords": "Confirm Passwords", "components.alreadyHavingAccountSignIn": "Already Having Account Sign In", "components.enterYourPassword": "Enter Your Password", "components.unlock": "Unlock", "components.enterPasswords": "Enter Passwords", "components.resestPassword": "Resest Password", "components.pageNotfound": "Page not Found", "youWillBeRedirectedTo.usersList": "You Will Be Redirected To Users List", "components.goToHomePage": "Go To Home Page", "components.sorryServerGoesWrong": "Sorry Server Goes Wrong", "components.persistentDrawer": "Persistent Drawer", "return_to_the_branch": "Return To The Branch", "components.withHtml": "With Html", "Select": "Select", "components.prompt": "Prompt", "handover_to_same_Pickup_location": "Handover To Same Pickup Location", "components.withDescription": "With Description", "return_to_same_Pickup_location": "Return to same Pickup location", "components.success": "Success", "components.passwordPrompt": "Password Prompt", "components.warning": "Warning", "components.customIcon": "Custom Icon", "components.customStyle": "Custom Style", "components.basic": "Basic", "components.submit": "Submit", "components.compose": "Compose", "components.sendMessage": "Send Message", "components.addNewTasks": "Add New Tasks", "components.addToCart": "Add To Cart", "components.payNow": "Pay Now", "components.print": "Print", "components.cart": "<PERSON><PERSON>", "components.viewCart": "View Cart", "components.checkout": "Checkout", "widgets.QuickLinks": "Quick Links", "widgets.upgrade": "upgrade", "widgets.app": "App", "widgets.addNew": "Add New", "widgets.orderDate": "Order Date", "widgets.setFeedbacks": "setFeedbacks", "setFeedbacks": "setFeedbacks", "subsetFeedbacks": "Sub setFeedbacks", "widgets.subsetFeedbacks": "SubsetFeedbacks", "widgets.trackingNumber": "Tracking Number", "widgets.action": "Action", "widgets.designation": "Designation", "widgets.subject": "subject", "widgets.send": "Send", "bannerable_data": "bannerable data", "widgets.saveAsDrafts": "Save As Drafts", "widgets.onlineSources": "Online Sources", "widgets.lastMonth": "Last Month", "widgets.widgets": "Widgets", "widgets.listing": "Listing", "widgets.paid": "Paid", "widgets.refunded": "Refunded", "widgets.done": "Done", "widgets.pending": "Pending", "widgets.cancelled": "Cancelled", "cancelled": "Cancelled", "deliver_lat": "Deliver Lat", "deliver_lng": "Deliver Lng", "Car_Delivery": "Car Delivery", "Car_delivery_service": "Handover In Another City Service", "basicinformation": "Basic Information", "deliver_address": "Deliver Address", "from.km.label": "from km", "working.days": "Working Days", "mapandlocation": "Map And Location", "from.km.placeholder": "from km", "sidebar.service": "Extra Service ", "sidebar.extraservices": "Extra Services", "rate": "Rate", "extraService.details": "ExtraService Details", "serviceValue": "Service Value", "arDescription.label": "Ar Description", "enDescription.label": "En Description", "arDescription.placeholder": "Ar Description", "enDescription.placeholder": "En Description", "serviceid": "Service ID", "paytype": "Pay Type", "showFor": "Show For", "isActive": "Is Active", "allyExtraService": "Extra Service", "ServiceValue.label": "Service Value", "ally": "Ally", "one.time": "One Time", "free": "Free", "isRequired": "Is Required", "sidebar.extraservice": "Extra Service", "deliveryPrice.label": "delivery price", "extraservice": "Extra Service", "deliveryPrice.placeholder": "delivery price", "delivery_price.placeholder": "Delivery Price", "handoverprice.label": "handover price", "Handover_price.placeholder": "Handover Price ", "handoverprice": "Car handover fee", "handover_price.placeholder": "Handover Price ", "handoverprice.placeholder": "handover price", "from.kilometer.need": "please enter from kilometer value", "To.kilometer.need": "please enter to kilometer value", "to.km.label": "to km", "Max10number": "Max 10 number", "Gulf_ID": "Gulf national ID", "Max20number": "Max 20 number", "IqamaFormat": "The Iqama no. is invalid", "iqama.no": "<PERSON><PERSON><PERSON> ", "branchAddress": "Branch Address", "couponDiscount": "Coupon Discount", "BranchPhoneNumber": "Branch Phone Number", "not_b2c": "Not B2C", "B2B_carwah_Bussines": "B2B (Carwah business)", "isFree": "Is Free", "canHandoverInAntherCity": "Can Handover In Another City", "Other": "Other", "sidebar.statistics": "Statistics", "to.km.placeholder": "to km", "widgets.approve": "Approve", "refund.money": "Money Refunded Successfully", "widgets.following": "Following", "widgets.follow": "follow", "widgets.graphs&Charts": "Graphs & Charts", "widgets.open": "Open", "widgets.bounced": "Bounced", "widgets.spam": "Spam", "widgets.unset": "Unset", "widgets.bandwidthUse": "Bandwidth Use", "widgets.dataUse": "Data Use", "widgets.unsubscribe": "Unsubscribe", "widgets.profile": "Profile", "widgets.messages": "Messages", "widgets.support": "Support", "widgets.faq(s)": "Faq(s)", "all": "All", "show.cars": "Show Cars", "userNid.placeholder": "ID No", "rate.placeholder": "Rate Value", "widgets.upgradePlains": "Upgrade Plains", "widgets.logOut": "Log Out", "widgets.mail": "Mail", "bookings": "Bookings ", "widgets.adminTheme": "Admin Theme", "widgets.wordpressTheme": "Wordpress Theme", "widgets.addToCart": "Add To Cart", "widgets.plan": "Plan", "widgets.basic": "Basic", "sidebar.EditRole": "Edit Role", "arName": "Arabic Name", "Ally.ID": "ID", "sidebar.companies": "Ally Companies", "company.managerName": "Manager Name", "phoneNumber": "phone Number", "CarTimeLine": "Car TimeLine", "CarId": "Car ID ", "car_version_id": "car_version_id", "acriss_fuel_id": "Acriss Fuel ID", "is_rent_to_own": "is rent to own", "months_price": "months price", "acriss_transmission_id": "Acriss Transmission ID", "acriss_category_id": "Acriss Category ID", "months_prices_reflection": "months prices reflection", "transmission": "Transmission", "make_id": "Make ID", "company.Class": "Class", "email": "Email", "roledeletedsuccessfully": "Role Deleted Successfully", "sidebar.cars": "Listing  Cars", "arDescription": "Arabic Description", "enName": "English Name", "enDescription": "English Description", "RoleEditSuccessfully": "Role Edit Successfully", "NoChangestoEdit": "No Changes to Edit", "widgets.pro": "Pro", "company": "company", "widgets.startToBasic": "Start To Basic", "widgets.upgradeToPro": "Upgrade To Pro", "widgets.upgradeToAdvance": "Upgrade To Advance", "widgets.comparePlans": "Compare Plans", "widgets.free": "Free", "widgets.frequentlyAskedQuestions": "Frequently Asked Questions", "widgets.searchIdeas": "Search Ideas", "widgets.startDate": "Start Date", "widgets.endDate": "End Date", "widgets.category": "Category", "widgets.apply": "Apply", "widgets.yesterday": "Yesterday", "widgets.totalOrders": "Total Orders", "widgets.totalVisitors": "Total Visitors", "widgets.typeYourQuestions": "Type Your Questions", "widgets.username": "Username", "widgets.password": "Password", "widgets.signIn": "Sign In", "widgets.enterYourPassword": "Enter Your Password", "widgets.alreadyHavingAccountLogin": "Already Having Account <PERSON>gin", "widgets.composeMail": "Compose Mail", "widgets.issue": "Issue", "widgets.recentChat": "Recent Chat", "widgets.previousChat": "Previous Chat", "widgets.all": "All", "widgets.filters": "Filters", "widgets.deleted": "Deleted", "widgets.starred": "Starred", "widgets.frontend": "Frontend", "widgets.backend": "Backend", "widgets.api": "Api", "Enableonlinepayment": "Enable Online Payment", "onlinepayment": "Online Payment", "widgets.simpleAppBar": "Simple App Bar", "widgets.recents": "Recents", "widgets.cardLink": "Card Link", "you.must.select.car": "You Must Select Car", "carsdeletedsuccessfully": "Cars Deleted Successfully", "widgets.anotherLink": "Another Link", "widgets.cardSubtitle": "card Subtitle", "widgets.confirmationDialogs": "Confirmation Dialogs", "widgets.deletableChip": "Deletable Chip", "widgets.customDeleteIconChip": "Custom Delete Icon Chip", "widgets.openAlertDialog": "Open <PERSON><PERSON>", "widgets.openResponsiveDialog": "Open Responsive Dialog", "widgets.openSimpleDialog": "Open Simple Dialog", "widgets.openFormDialog": "Open Form Dialog", "widgets.follower": "Follower", "widgets.important": "Important", "widgets.private": "Private", "widgets.openLeft": "Open Left", "widgets.openRight": "Open Right", "widgets.openTop": "Open Top", "widgets.openBottom": "Open Bottom", "widgets.selectTripDestination": "Select Trip Destination", "widgets.pinnedSubheaderList": "Pinned Subheader List", "widgets.singleLineItem": "Single Line Item", "widgets.optionA": "Option A", "widgets.optionB": "Option B", "widgets.optionC": "Option C", "widgets.optionM": "Option M", "widgets.optionN": "Option N", "widgets.optionO": "Option O", "widgets.customColor": "Custom Color", "widgets.centeredTabs": "Centered Tabs", "widgets.multipleTabs": "Multiple Tabs", "widgets.preventScrolledButtons": "Prevent Scrolled Buttons", "widgets.browse": "Browse", "widgets.formValidate": "Form Validate", "widgets.code": "Code", "widgets.company": "Company", "widgets.price": "Price", "widgets.change": "Change", "widgets.high": "High", "widgets.low": "Low", "widgets.volume": "Volume", "widgets.personalDetails": "Personal Details", "widgets.occupation": "Occupation", "widgets.companyName": "Company Name", "widgets.phoneNo": "Phone No", "widgets.updateProfile": "Update Profile", "widgets.reject": "Reject", "widgets.exportToExcel": "Export To Excel", "widgets.workWeek": "Work Week", "widgets.agenda": "Agenda", "widgets.conference": "Conference", "widgets.multilevel": "Multilevel", "widgets.dailySales": "Daily Sales", "widgets.today": "Today", "widgets.campaignPerformance": "Campaign Performance", "widgets.supportRequest": "Support Request", "widgets.usersList": "Users List ", "widgets.lastWeek": "Last Week", "themeOptions.sidebarOverlay": "Sidebar Overlay", "themeOptions.sidebarBackgroundImages": "Sidebar Background Images", "themeOptions.appSettings": "App Settings", "themeOptions.sidebarImage": "Sidebar Image", "themeOptions.miniSidebar": "Mini Sidebar", "themeOptions.boxLayout": "Box Layout", "themeOptions.rtlLayout": "Rtl Layout", "themeOptions.darkMode": "Dark Mode", "themeOptions.sidebarLight": "Light", "themeOptions.sidebarDark": "Dark", "button.cancel": "Cancel", "button.add": "Add", "button.update": "Update", "button.reply": "Reply", "button.delete": "Delete", "button.yes": "Yes", "button.viewAll": "View All", "button.like": "Like", "button.assignNow": "Assign Now", "button.seeInsights": "See Insights", "sidebar.dateTimePicker": "Date & Time Picker", "components.summary": "Summary", "hint.whatAreYouLookingFor": "What are You Looking For", "components.yesterday": "Yesterday", "components.last7Days": "Last 7 Days", "components.last1Month": "Last 1 Month", "components.last6Month": "Last 6 Month", "components.spaceUsed": "Space Used", "components.followers": "Follower", "components.trending": "Trending", "components.paid": "Paid", "components.refunded": "Refunded", "components.done": "Done", "components.pending": "Pending", "components.cancelled": "Cancelled", "components.approve": "Approve", "components.week": "Week", "components.month": "Month", "components.year": "Year", "components.today": "Today", "components.popularity": "Popularity", "components.drafts": "Drafts", "components.sent": "<PERSON><PERSON>", "components.trash": "Trash", "components.all": "All", "components.do": "Do", "components.title": "Title", "components.projectName": "Project Name", "components.companyName": "Company Name", "ally.name": "Ally Name", "managerId": "Manager ID", "Rent.per": "Rent(Day,Week,Month)", "components.openAlert": "Open Alert", "ManagerTimeLine": "Manager <PERSON><PERSON><PERSON>", "en_name": "English Name", "ar_name": "Ar Name ", "category": "Category", "added_by": "Added By", "updated_by": "Updated By ", "FeatureId": "Feature ID", "en_title": "English title", "ar_title": " Arabic title", "ar_description": "Arabic description", "en_description": "English description ", "pay_type": "Pay Type ", "one_time": "one time", "Icon_url": "Extra Services Icon ", "is_special": "Is Special", "gender": "Gender", "driver_license": "Driver License Number", "driver_license_expire_at": "Driver License Expiry Date", "passport_number": "Passport Number", "license_front_image": "Driver License", "company_name": "Company Name", "passport_expire_at": "Passport Expiry Date", "license_selfie_image": "Driver License with <PERSON>ie", "passport_front_image": "Passport Front Image", "customer_class": "Customer Class", "blocking_reason": "Blocking Reason", "blocking_status": "Blocking Status", "border_number": "Boarder Number", "national_id_expire_at": "National ID Expiry Date", "homepage_icon_url": "Extra Services Image", "components.slideInAlertDialog": "Slide In Alert Dialog", "components.openFullScreenDialog": "Open Full Screen Dialogs", "components.basicChip": "Basic Chip", "components.clickableChip": "Clickable Chip", "components.left": "Left", "components.right": "Right", "national_id_version": "Version", "nid_image": "ID Image", "components.expansionPanel1": "Expansion Panel 1", "components.expansionPanel2": "Expansion Panel 2", "components.generalSetting": "General Setting", "components.advancedSettings": "Advanced Settings", "components.firstName": "First Name", "components.lastName": "Last Name", "components.occupation": "Occupation", "components.phoneNo": "Phone No", "components.address": "Address", "components.city": "City", "components.state": "State", "components.zipCode": "Zip Code", "components.social Connection": "Social Connection", "widgets.buyMore": "Buy More", "widgets.trafficChannel": "Traffic Channel", "widgets.stockExchange": "Stock Exchange", "widgets.tweets": "Tweets", "widgets.ourLocations": "Our Locations", "widgets.sales": "Sales", "widgets.to": "To", "widgets.shipTo": "Ship To", "widgets.description": "Description", "widgets.unitPrice": "Unit Price", "widgets.total": "Total", "widgets.note": "Note", "widgets.chipWithAvatar": "Chip With Avatar", "widgets.chipWithTextAvatar": "Chip With Text Avatar", "widgets.chipWithIconAvatar": "Chip With Icon Avatar", "widgets.customClickableChip": "Custom Clickable Chip", "widgets.outlineChip": "Outline Chip", "widgets.disableChip": "Disable Chip", "widgets.alertDialog": "<PERSON><PERSON>", "widgets.animatedSlideDialogs": "Animated Slide Dialogs", "widgets.fullScreenDialogs": "Full Screen Dialogs", "widgets.formDialogs": "Form Dialogs", "widgets.simpleDialogs": "Simple Dialogs", "widgets.responsiveFullScreen": "Responsive Full Screen", "widgets.primary": "Primary", "widgets.social": "Social", "widgets.user": "User", "widgets.admin": "Admin", "widgets.permanentdrawer": "Permanent Drawer", "widgets.persistentdrawer": "Persistent Drawer", "widgets.swiches": "Swiches", "widgets.horizontalLinearAlternativeLabel": "Horizontal Linear Alternative Label", "widgets.horizontalNonLinearAlternativeLabel": "Horizontal Non Linear Alternative Label", "widgets.notifications": "Notifications", "widgets.basicAlert": "Basic Alert", "widgets.successAlert": "Success Alert", "widgets.warningAlert": "Warning Alert", "widgets.reactAutoSuggest": "React Auto Suggest", "widgets.components": "Components", "widgets.inputAdornments": "Input Adorements", "widgets.multiSelectList": "Multi Select List", "widgets.contextualColoredTable": "Contexual Colored Table", "widgets.updateYourEmailAddress": "Update Your Email Address", "widgets.selectADefaultAddress": "Select A Default Address", "widgets.activity": "Activity", "widgets.basicCalendar": "Basic Calendar", "widgets.culturesCalendar": "Cultures Calendar", "widgets.dragAndDropCalendar": "Drag And Drop Calendar", "widgets.quillEditor": "Quill Editor", "widgets.reactDND": "React DND", "widgets.dragula": "<PERSON><PERSON><PERSON>", "button.acceptTerms": "Accept Terms", "button.reject": "Reject", "button.addNew": "Add New", "button.goToCampaign": "Go To Campaign", "button.viewProfile": "View Profile", "button.sendMessage": "Send Message", "button.saveNow": "Save Now", "button.pen": "Pen", "button.search": "Search", "button.downloadPdfReport": "Download Pdf Report", "button.primary": "Primary", "button.secondary": "Secondary", "button.danger": "Danger", "button.info": "Info", "button.success": "Success", "button.warning": "Warning", "button.link": "Link", "button.smallButton": "Small Button", "button.largeButton": "Large Button", "button.blockLevelButton": "Block Level Button", "button.primaryButton": "Primary Button", "button.button": "<PERSON><PERSON>", "button.save": "Save", "button.saveAndPay": "Save & Pay", "button.openMenu": "Open Menu", "button.openWithFadeTransition": "Open With Fade Transition", "button.openPopover": "Open Popover", "button.accept": "Accept", "button.click": "Click", "button.complete": "Complete", "button.back": "Back", "insurance": "Insurance", "button.next": "Next", "button.completeStep": "Complete Step", "button.error": "Error", "button.writeNewMessage": "Write New Message", "button.saveChanges": "Save Changes", "button.addNewUser": "Add New User", "button.more": "More", "hint.searchMailList": "Search Mail List", "widgets.AcceptorrRejectWithin": "Accept or reject within", "widgets.quoteOfTheDay": "Quote Of The Day", "widgets.updated10Minago": "Updated 10 min ago", "widgets.personalSchedule": "Personal Schedule", "widgets.activeUsers": "Active Users", "widgets.totalRequest": "Total Request", "widgets.new": "New", "widgets.ShareWithFriends": "Share with friends!", "widgets.helpToShareText": "Help us spread the world by sharing our website with your friends and followers on social media!", "widgets.thisWeek": "This Week", "widgets.howWouldYouRateUs": "How would you rate us?", "widgets.booking": "Booking", "widgets.confirmed": "Confirmed", "widgets.monthly": "Monthly", "widgets.weekly": "Weekly", "widgets.target": "Target", "widgets.totalActiveUsers": "Total Active Users", "sidebar.user": "User", "sidebar.miscellaneous": "Miscellaneous", "sidebar.promo": "Promo", "themeOptions.themeColor": "Theme Color", "module.inbox": "Inbox", "module.drafts": "Drafts", "module.sent": "<PERSON><PERSON>", "module.trash": "Trash", "module.spam": "Spam", "module.frontend": "Frontend", "module.backend": "Backend", "module.api": "Api", "module.issue": "Issue", "common.emailPrefrences": "Email <PERSON>frences", "components.myProfile": "My Profile", "sidebar.gettingStarted": "Getting Started", "widgets.deadline": "Deadline", "widgets.team": "Team", "widgets.projectManagement": "Project Management", "widgets.latestPost": "Latest Post", "widgets.projectTaskManagement": "Project Task Management", "widgets.selectProject": "Select Project", "widgets.activityBoard": "Activity Board", "widgets.checklist": "Checklist", "sidebar.shop": "Shop", "sidebar.cart": "<PERSON><PERSON>", "sidebar.checkout": "Checkout", "components.product": "Product", "components.quantity": "Quantity", "components.totalPrice": "Total Price", "components.removeProduct": "Remove Product", "components.mobileNumber": "Mobile Number", "sidebar.managers": "Managers", "sidebar.manager": "Managers", "manager": "manager", "copyBannerLink": "Copy banner link", "components.address2Optional": "Address 2 (Optional)", "components.country": "Country", "components.zip": "Zip", "components.saveContinue": "Save & Continue", "components.placeOrder": "Place Order", "components.payment": "Payment", "components.billingAddress": "Billing Address", "components.ShippingAddressText": "Shipping address is the same as billing address.", "components.CartEmptyText": "Your Shopping Cart Is Empty!", "components.NoItemFound": "No Item Found", "components.goToShop": "Go To Shop", "components.cardNumber": "Card Number", "components.expiryDate": "Expiry Date", "components.cvv": "CVV", "components.nameOnCard": "Name On Card", "components.confirmPayment": "Confirm Payment", "choose.an.image": "Choose an image", "sidebar.saas": "SAAS", "sidebar.multiLevel": "MultiLevel", "sidebar.level1": "Level 1", "sidebar.level2": "Level 2", "sidebar.boxed": "Boxed", "sidebar.extensions": "Extensions", "sidebar.imageCropper": "Image Cropper", "sidebar.videoPlayer": "Video Player", "sidebar.dropzone": "Dropzone", "widgets.baseConfig": "Base Config", "widgets.customControlBar": "Custom Control Bar", "widgets.withDownloadButton": "With Download Button", "widgets.httpLiveStreaming": "HTTP Live Streaming", "widgets.keyboardShortcuts": "Keyboard Shortcuts", "three_months": "three months", "nine_months": "nine months", "two_months": "two months", "four_months": "four months", "five_months": "five months", "six_months": "six months", "seven_months": "seven months", "eight_months": "eight months", "ten_months": "ten months", "eleven_months": "eleven months", "twelve_months": "twelve months", "is_unlimited_free": "Is Unlimited Free", "unlimited_fee_per_day": "unlimited fee per day", "distance_by_day": "distance by day", "toggle.title": "Activate/Deactivate", "toggle.confirmMessage": "Are you sure you want to activate /deactivate this record?", "activate.item": "Activate {item}", "deactivate.item": "Deactivate {item}", "button.useDefaultImage": "Use Default Image", "button.cropImage": "Crop Image", "widgets.preview": "Preview", "widgets.croppedImage": "Cropped Image", "widgets.transactionList": "Transaction List", "widgets.transferReport": " Transfer Report", "widgets.expenseCategory": "Expense Category", "widgets.upcomingEvents": "Upcoming events", "widgets.OngoingProjects": "Ongoing Projects", "widgets.ProjectsetFeedbacks": "Project setFeedbacks", "widgets.LiveChatSupport": "Live Chat Support", "sidebar.projects": "projects", "sidebar.projectDetail": "project Detail", "sidebar.makes": "Makes", "sidebar.reports": "reports", "version": "Version", "btn.signin": "<PERSON><PERSON>", "yes": "Yes", "no": "No", "activate.successfully": "Activated successfully", "End date must be greater or equal to start date": "End date must be greater or equal to start date", "u.want.to.activate.this.coupon": "you want to activate this coupon?", "thisfieldisrequired": "this field is required", "Bank.Transfer.Refund": "Bank Transfer Refund", "Deactivated.successfully": "Deactivated.successfully", "labels.rememberMe": "Remember me", "u.want.to.deactivate.this.coupon": "you want to deactivate this coupon", "userdeletedsuccessfully": "user deleted successfully", "close": "Close", "banners": "banners", "ally_conditions": "ally conditions ", "ally_features": "ally features ", "extra_services": "extra services ", "rates": "rates ", "u.want.delete.user": "you want to delete this user", "companies.management": "Companies Management", "validation.requiredField": "Required field", "validation.thisFieldIsRequired": "This field is required", "minRentPrice.label": "Minimum rent price ", "minRentPrice.placeholder": "Minimum rent price ", "minRentDays.label": "Minimum rent days ", "minRentDays.placeholder": "Minimum rent days ", "numOfDays.label": "No. of days", "numOfDays.placeholder": "No. of days", "free_delivery": "Free delivery", "free_handover": "Free handover", "free_days": "Freedays ", "validation.emailFormat": "Please enter right email format", "validation.emailFormatPleaseFormat": "Please enter a valid email address", "validation.strongPasswordRequired": "Password length should be at least 8 digits contains one letter, one number, and one special character", "validation.emailAlreadyRegistered": "This email address is registered", "validation.passwordLengthError": "Password should be of minimum 8 characters length", "validation.passwordShouldBeLike": "Password length should be at least 8 digits contains one letter, one number, and one special character", "validation.tooShortName": "The name is too short; Please edit it", "validation.tooLongName": "Min. 1, Max. 100 character", "validation.tooShortNameYour": "Your name is too short; Please edit it", "validation.tooLongNameYour": "Your name is too long; Please edit it", "validation.invalidMobileNumber": "Please enter right mobile number", "validation.namesCannotBeBlank": "Names Can not be blank", "validation.fromMustBeLessThanTo": "End date can't be before start date", "validation.thisImageIsRequired": "This image is required", "validation.commercialRegestrationMax15Char": "Commercial Regestration must be at most 15 characters", "errors.login": "Incorrect data, please enter right data", "upladImageError": "Please select images with extensions .jpeg, .png or .gif", "email.address": "Email address", "data.range": "Select date range", "placeholder.password": "Password", "firstName.label": "First Name", "lastName.label": "Last Name", "middleName.label": "Middle Name", "email.label": "Email Address", "firstName.placeholder": "First Name", "lastName.placeholder": "Last Name", "companyName.label": "Company Name", "userName.placeholder": "User name", "userName.label": "User name", "arName.label": "Name (Ar)", "arName.placeholder": "Name (Ar)", "enName.label": "Name (En)", "enName.placeholder": "Name (En)", "companyName.placeholder": "Company Name", "middleName.placeholder": "Middle Name", "customerName.placeholder": "Customer Name", "success.create.allyCompany": "New Ally Added Successfully", "success.create.coupon": "New Coupon Added Successfully", "success.edit.allyCompany": "<PERSON> Edited Successfully", "bookingNo.": "Booking No.", "bookingNo.placeholder": "Booking No./ID", "bookingId.placeholder": "Booking ID", "featureEnName": "English Features Name", "featureArName": "Arabic Feature Name", "success.edit.coupon": "Coupon Edited Successfully", "featureicon": "Features Icon", "displayOrder": "Display order", "allyName.placeholder": "Ally Name", "email.placeholder": "Email Address", "nid.placeholder": "National ID", "nationalId.label": "National ID", "nationalId.placeholder": "National ID", "next": "next", "idPhoto": "ID Photo", "isParent": "Is Parent", "nameAr.placeholder": "Arabic Name", "nameEn.placeholder": "English Name", "success.change.installment.status": "Installment Status Changed Successfully", "displayOrder.placeholder": "Display Order", "components.parent.feature": "Parent Feature ", "FeatureDeletedSuccessfully": "Feature Deleted Successfully", "RateDeletedSuccessfully": "Rate Deleted Successfully", "BranchDeletedSuccessfully": "Branch Deleted Successfully", "nameAr.label": "Arabic Name", "nameEn.label": "English Name ", "displayOrder.label": "Display Order ", "name.placeholder": "Feature Name", "components.category.feature": "Feature Category ", "FeaturesID": "Features ID", "featureId": "Feature ID", "extraServiceId": "Extra Service ID", "VersionsTimeLine": "Versions TimeLine", "rateId": "Rate ID", "otherColor": "Other Color", "RatesTimeLine": "Rates TimeLine", "Done successfully": "Done successfully", "Editing this booking period will close the pending extensions": "Editing this booking period will close the pending extensions", "featureparents": "Parent Feature", "feature.icon": "Feature Icon", "previous": "previous", "sidebar.add": "Add", "rental.price": "Rental Price", "car.setFeedbacks": "setFeedbacks", "Installments": "Installments", "1st Installment-booking": "1st Installment", "final installment": "Final installment", "rental.plans": "Choose <PERSON>", "ally_declined": "<PERSON> Declined", "paid_amount": "<PERSON><PERSON>", "refunded_amount": "Refunded Amount", "refunded_at": "Refunded at", "Booking No": "Booking No", "Ticket.ID": "Ticket ID", "Category.feedback": "Category", "refunded_type": "Refunded Type", "partial_refund": " Partial Refund", "decline_reason": "Decline Reason", "full_refund": " Full Refund", "refund_with_dedcution": " Refund with Deduction", "Would you like to proceed?": "Would you like to proceed ? ", "You've chosen an ally who has previously declined this booking": "You've chosen an ally who has previously declined this booking", "Rental Installments": "Rental Installments", "rent-to-own": "Rent To Own", "AllyName": "Ally Name", "rentals.plans": "Rentals Plans", "managerName.placeholder": "Manger Name", "setFeedbacksChangedSuccessfully": " setFeedbacks Changed Successfully", "customerId.placeholder": "Customer ID", "officeNumber.label": "Office number", "officeNumber.placeholder": "Office number", "kilometers": "kilometers", "car.count.ready.rent": "Number of cars ready for rent ", "No data found": "No Record Found!!!", "title": "Title", "BookingTimeLine": "Booking TimeLine", "notpayed": "Not Paid", "payed": "Paid", "Confirm": "Confirm", "PICKUP_OVERDUE": "Pickup Overdue", "Due Amount": "Due Amount", "Invoicing this booking will close the pending extensions as well": "Invoicing this booking will close the pending extensions as well", "confirmed": "Confirmed", "dsitance.label": "Distance", "handover_price": "Handover Price", "handover_price.label": "Handover Price", "paymentsetFeedbacks": "Payment setFeedbacks", "pleasecheckdiminssion300x180": "Invalid image, Please upload image dimension 300x180 ", "pleasecheckdiminssion1104x596": " Invalid image, Please upload image dimension 1104x596", "pleasecheckdiminssion600x400": "Invalid image, Please upload image dimension 600x400", "vehicle.type": "Vehicle Type", "delivery_price.label": "Delivery Price", "branch.managerName": "manager Name", "car.thumbnail.image": "Car.Thumbnail Image", "allyname": "Ally Name", "businessRental.details": "Business Rental Details", "allyname.placeholder": "Ally Name", "persolalImage": "Personal Image", "Mr": "Mr.", "feature.details": "Feature Details ", "Mrs": "Mrs.", "Miss": "Miss", "carModelDeletedSuccessfully": "car Model Deleted Successfully", "ModelID": "ID", "sidebar.models": "Models", "ModelName": "Model Name", "goBack": "go Back", "White": "whiteb", "car.year": "Year", "car.color": "Color", "car.km": "KM", "car.Plan": "Plan", "ChangesetFeedbacks": "Change setFeedbacks", "pickupdatehaspassed": "pickupdate has passed", "Ms.": "Ms.", "car.PlateNumber": "Plate No.", "common.add": "Add", "common.edit": "Edit", "common.addSomething": "Add {something}", "common.editSomething": "Edit {something}", "common.deleteSomething": "Delete {something}", "common.details": "Details", "common.arName": "Name (Ar)", "car.state": "State", "Car Media": "Car Media", "components.rent0wn": "Rent Type", "common.enName": "Name (En)", "1st Installment": "1st Installment", "plateNo.placeholder": "Plate No", "plateNo": "Plate No", "Plate No": "Plate No", "Monthly Installment": "Monthly Installment", "No. of months": "No. of months", "Available after": "Available after", "Final Installment": "Final Installment", "KM": "KM", "closingReasons": "Closing Reasons", "Rent-to-Own": "Rent-To-Own", "RENTAL": "Rental", "RENT_TO_OWN": "Rent-to-Own", "modelArName.label": "Model Name (Ar)", "modelArName.placeholder": "Model Name (Ar)", "modelEnName.label": "Model Name (En)", "Add Note": "Add Note", "modelEnName.placeholder": "Model Name (En)", "common.confirm": "Confirm", "Update Extra Service": "Update Extra Service", "change Duration": "Change Duration", "Change": "Change", "Add": "Add", "common.actions": "Actions", "common.cancel": "Cancel", "common.id": "ID", "common.email": "Email", "clear": "Clear", "login.header": "Login to Carwah dashboard", "forgotPassword": "Forget your password?", "company.name": "Company name", "manager.name": "Manager name", "mobile.number": "Mobile number", "mobile.number.placeholder": "Mobile number", "company.logo": "Company Logo", "change.logo": "Change Logo", "changesetFeedbacks": "Change setFeedbacks", "extend": "Extend", "customers.setFeedbacks": "Customers setFeedbacks", "use.original.logo": "Use previous logo", "commercial.registration": "Commercial Registration", "commercialRegestrationImage": "Commercial Registration Image", "commercialRegestration": "Commercial Registration", "commercialRegestration.label": "Commercial Registration", "commercialRegestration.placeholder": "Commercial Registration", "bankCardImage": "Bank Card Image", "bank_card_image": "Bank Card Image", "class": "Class", "commision_rate": "Commision Rate", "license.image": "License Image", "makes": "Makes", "models": "Models", "model": "model", "versions": "Versions", "common.delete": "delete", "make": "Make", "car": "Car", "addcar": "car", "paymentMethod": "Payment Method", "are.u.sure.?": "Are You Sure ? ", "u.want.delete.model": "You Want To Delete This Model", "u.want.delete.role": "You Want To Delete This Role ", "u.want.delete.branch": "you want to delete this branch and the related cars?", "u.want.delete.banner": "You Want To Delete This Banner", "ServiceDeletedSuccessfully": "Service Deleted Successfully", "u.want.delete.service": "You Want To Delete This Service", "u.want.delete.version": "You Want To Delete This Version ", "u.want.delete.item": "You Want To Delete This Item", "u.want.delete.feature": "You Want To Delete This Feature", "success.create.manager": "Manager Addedd Successfully", "u.want.delete.manager": "You Want To Delete This Manager ", "ManagerDeletedSuccessfully": "Manager Deleted Successfully", "carsupdatedsuccessfully": " Cars Updated Successfully", "u.want.delete.rate": "You Want To Delete This Rate", "CASH": "Cash", "districtNameAr.label": " Arabic District Name", "districtNameEn.label": "English District Name", "AddManager": "Add Manager", "EditManager": "Edit Manager", "referenceCode": "Reference Code ", "referenceCode.label": "Reference Code ", "referenceCode.placeholder": "Reference Code ", "Extraservice-Icon": "Extra service Icon", "EditModel": "Edit Model", "AddModel": "Add Model", "AddUser": " Add User", "EditUser": "Edit User", "EditFeature": "Edit Feature", "AddFeature": "Add Feature", "EditRate": "Edit Rate", "AddRate": "Add Rate", "AddExtraService": "Add Extra Service", "EditExtraService": "Edit Extra Service", "VISA": "Visa", "EditBranch": "Edit Branch", "EditVersion": "Edit Version", "AddVersion": "Add Version", "AddBranch": "Add Branch", "addBooking": "Add Booking", "SAR": "SAR", "addCompany": "Add Company", "EditBooking": "Edit Booking", "EditCompany": "Edit Company", "addCustomer": "Add Customer", "EditCustomer": "Edit Customer", "suggestedPricePerDay.label": "Suggested price per day", "suggestedPricePerDay.placeholder": "Suggested price per day", "insuranceType": "Insurence type", "availableCars": "Available cars", "delete.make.dialog.msg": "Are you sure you want to delete this record?", "bookings.list.customerName": "Customer", "bookings.list.allyName": "Ally", "bookings.list.numberOfDaysToBeRented": "Rented days", "bookings.list.carRentPricePerDay": "Price/Day", "bookings.list.billingAmount": "Grand total", "rent.payed": "Paid", "success.create.service": "Extra Service Added successfully", "success.edit.extraservice": "Extra Service Edited successfully", "enTitle.label": "En Title", "enTitle.placeholder": "En Title", "arTitle.label": "Ar Title", "components.region": "Region", "rental.refundedAt": "Refunded At", "showfor": "Show For", "servicevalue": "Service Value", "arTitle.placeholder": "Ar Title", "serviceValue.label": "Service Value", "serviceValue.placeholder": "Service Value", "ExtraserviceIcon": "Extra Service Image", "rental.refundamount": "Refund Amount", "bookings.list.header.billingAmount": "Billing Amount", "bookings.list.paidAmount": "Insurance Amt.", "bookings.list.pickupCity": "Pickup City", "u.want.to.refund.this.Booking.to.u": "you want to refund this booking ", "u.want.to.refund.this.extension.to.u": "you want to refund this extension", "bookings.list.pickup": "Pickup Time", "bookings.list.delivery": "Return time", "fullrefund": "Full Refund", "refund": "Refund", "refund.done": "Refund Done Successfully", "refund.done.after.deduction": "Refund Done After Deduction ", "refundwithdeduction": "Refund With Deduction", "bookings.list.pickupDate": "Pickup Date", "bookings.list.pickupBranch": "Pickup Branch", "bookings.list.pickupShift": "Pickup Shift", "bookings.list.returnDate": "Return Date", "bookings.list.returnBranch": "Return Branch", "bookings.list.returnShift": "Return Shift", "bookings.list.bookingsetFeedbacks": "Booking setFeedbacks", "car.bookingsetFeedbacks": "Booking setFeedbacks", "endtime": "End Time", "starttime": "Start Time", "Unlimited.KM": "Unlimited KM", "saturday": "Saturday", "u.want.delete.customer": "You  Want Delete This Customer", "customerdeletedsuccessfully": "Customer Deleted Successfully", "sunday": "Sunday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "monday": "Monday", "two.month": "Two Months", "one.month": "One Month", "three.month": "Three Months", "four.month": "Four Months", "five.month": "Five Months", "six.month": "Six Months", "seven.month": "Seven Months", "eight.month": "Eight Months", "nine.month": "Nine Months", "ten.month": "Ten Month", "eleven.month": "Eleven Months", "twelve.month": "Twelve Months", "twentyfour.month": "Twenty Four  Months", "tuesday": "Tuesday", "branch.name": "Branch Name ", "note.required": "Note Field Required", "branch.managernumber": "Manager Number", "careditsuccessfully": "Car Updated Successfully", "sidebar.editcar": "Edit Car", "nochangestoEdit": "No Changes To Edit", "branch.id": "Branch ID", "common.print": "Print", "print": "Print", "branch.managername": "Manager<PERSON>ame", "resend.done": "Resend Booking Successfully", "rental.cancelReason": "Cancel Reason", "resendRental": "Resend Rental", "rental.IntegrationsetFeedbacks": "Rental Integratio nsetFeedbacks", "isApiIntegrated": "Is Api Integrated", "ApIIntegration": "ApI Integration", "New Customers": "New Customers", "branch.area": "City", "isDisplayed": "Show", "remove": "Remove", "branch.address": "Branch Address ", "Worktimeshifts": "Work Time Shifts", "branch.details": "Branch Details", "branch.location": "Branch Location", "companyName": "Company Name", "sidebar.addcar": "Add New Car ", "monthlyprice": "Monthly Price", "weeklyprice": "Weekly Price", "branchName": "Branch Name", "cityName": "City Name", "VechileType": "Vechile Type", "Make": "Make", "Model": "Model", "Year": "Year", "Transmission": "Transmission", "carColor": "Color", "RentperDay": "Rent/Day", "commisionRate.label": "Commision Rate", "commisionRate.placeholder": "Commision Rate", "DiscountonweeklyBooking": "Discount on weekly Booking", "DiscountonMonthlyBooking": "Discount on  Monthly Booking", "AdditionalDistanceCost": "Additional Distance Cost", "AccidentPenalty": "Accident Penalty", "bookings.list.bookingSubsetFeedbacks": "Booking Sub-setFeedbacks", "GuaranteeAmount": "Guarant<PERSON> Amount", "PlateNumber": "Plate Number", "Disnatceperweek": "Disnatce per week", "insurancevalue": "insurancevalue", "replies": "replies", "CarFeatures": "Car Features", "DistanceperMonth": "Distance per Month", "ally.CommercialRegistration": " Commercial Registration", "commercialRegistrationImage": "Commercial Registration Image", "ally.class": "Ally Class ", "ally.address": "Ally address", "company.details": "<PERSON>", "ally.location": "Ally location", "ally.officeNumber": "office phone number", "ally.allyRate ": "Ally Rate", "ally.rating": "<PERSON>", "ally.licenceImage": "License Image ", "ally.logo": "Company Logo Image", "ally.managerName": " Manager Name", "ally.commercialRegistrationImage": "Commercial Registration Image", "ally.setFeedbacks": "setFeedbacks ", "ally.phonenumber": "Mobile number.", "car.dailyprice": "Rent/Day ", "car.version": "Car Version", "ally.branches": "<PERSON>'s branches", "uploadImageError": "extensions are allowed: PNG, JPEG, JPG", "carmake": " Car Make", "carmodel": "Car Model", "carfeature": "Car Feature", "car.versions": "Versions", "carImage": "Car Image", "carimage": "Car Image ", "arTitle": "Ar Title", "enTitle": "En Title", "Carinsuranceimage": "Car Insurance Image", "DelivertoCustomerLocationCost": "Deliver to Customer LocationCost", "accidentPenalty": "Accident Penalty", "car.make": "Make", "version.id": "Version ID", "car.model": "Model", "model.name": "Model Name", "model.id": "Model ID", "model.makname": "Make Name", "model.details": "Model Details", "Insurance.value": "Insurance Value", "car.details": "Car Details", "car.branch.name": "Branch Name", "car.insurancetype": "Insurance Type", "car.guaranteeAmount": "Guarant<PERSON> Amount", "car.additionalDistanceCost": "Additional Distance Cost", "car.transmissionname": "Transmission", "additionalDistanceCost": "Additional Distance Cost", "bookings.list.option": " option", "caraddedsuccessfully": "Car Added Successfully", "car.availabilitysetFeedbacks": "Availability setFeedbacks", "customers.list.customerId": "Customer ID", "availability_status": "Availability status", "daily_price": "Daily Price", "additional_distance_cost": "additional distance cost", "customers.list.customerName": "Customer Name", "customers.list.customerPhoneNumber": "Customer phone number", "customers.list.customerEmail": "Customer email", "customers.list.numberOfCompletedTrips": "Completed trips", "customers.list.NumberOfAllTripsHasInvoicedsetFeedbacks": "Invoiced setFeedbacks trips", "customers.list.createdDate": "Created date", "customers.list.customersetFeedbacks": "Customer setFeedbacks", "amount.sar": "{amount}", "search.filter": "Search Filter", "NEW": "New", "CANCELLED": "Cancelled", "CONFIRMED": "Confirmed", "CLOSED": "Closed", "PENDING": "Pending", "pending": "Pending", "INVOICED": "Invoiced", "Basket": "Basket", "CUSTOMER_CARE": "Customer Care", "customer": "customer", "customers": "customers", "ALLY_DECLINED": "<PERSON> Declined", "LATE_DELIVERY": "Late Delivery", "CAR_RECEIVED": "Car Received", "PENDING_REVIEW": "Pending Review", "PENDING_EXTENSION": "Pending Extension", "LATE_CONFIRMATION": "Late Confirmation", "DUE_INVOICE": "Due Invoice", "BOOKING_EXTENDED": "Booking Extended", "NEW_REQUEST": "New request", "new_request": "New request", "insurance_id": "Insurance ID", "BASKET": "Basket", "rental.customer.details": "Customer Details", "rental.customerTitle": "Title", "rental.profileImage": "Profile Image", "rental.nameFirstNameLastName": "Full Name", "rental.nameFirstName": "First name", "rental.nameLastName": "Last Name", "rental.nameMiddleName": "Middle Name", "rental.companyName": "Company Name", "rental.userType": "User Type", "rental.mobileNumber": "Mobile Number", "rental.nationalIdIqama": "National Id Iqama", "rental.gender": "Gender", "feature": "feature", "success.create.feature": "Feature Added Successfully", "success.create.alliesRate": "Rate Added Successfully", "rental.passportNumber": "Passport Number", "passportNum.label": "Passport Number", "passportNum.placeholder": "Passport Number", "AddRow": "Add Row", "DeleteRow": "Delete Row", "Clearvaluesofrow": "Clear Values Of Row", "rental.town": "Town", "rental.dateOfBirth": "Date Of Birth", "rental.age": "Age", "rental.totalInvoicedBookings": "Total Invoiced Bookings", "rental.driverSLicenseIfFound": "Driver's license number", "rental.driverSLicenseImagesFrontBackIfFound": "Driver's license image", "rental.driverSLicensesetFeedbacks": "Driver's license setFeedbacks", "rental.bookingNumber": "Booking Serial", "rental.bookingNumber.placeholder": "Booking Serial", "rental.pickupLocation": "Pickup City", "rental.pickupDateTime": "Pickup Date/Time", "rental.dropoffDateTime": "Drop off Date/Time", "rental.dropoffTime": "Drop off time", "sar/day": "{price} SAR/day", "rental.pickupBranchName": "Pickup Branch name", "rental.returnLocation": "Return Location", "rental.returnBranchName": "Return branch name", "rental.pickupDateAndTime": "Pickup date and time", "rental.returnDateAndTime": "Return date and time", "rental.priceDay": "Price per day", "rental.totalRentalDays": "Total Rental Days", "rental.bookingsetFeedbacks": "Booking setFeedbacks", "rental.bookingSubsetFeedbacks": "Sub-setFeedbacks", "sidebar.banners": "Banners", "BannerAddSuccessfully": "Banner Added Successfully", "banner": "Banner", "EnImage": "English Image", "ArImage": "Arabic Image", "bannerID": "ID", "ALL": "All", "createdAt": "Created At", "sidebar.EditBanner": "Edit Banner", "sidebar.AddBanner": "Add Banner", "bannerdeletedsuccessfully": "Banner Deleted Successfully", "sortorder.label": "Sort Order", "sortorder.placeholder": "Sort Order", "bannereditedsuccessfully": "Banner Edited Successfully", "rental.paymentType": "Payment Type", "rental.insuranceType": "Insurance Type", "Do you refund to the customer's wallet?": "Do you refund to the customer's wallet?", "Wallet Refund": "Wallet Refund", "rental.totalInsuranceAmount": "Total insurance amount", "rental.totalBookingAmount": "Grand total", "Unable to send request to ally": "Unable to send request to ally", "rental.guaranteeAmountIfAny": "Guarant<PERSON> Amount", "wallet": "Wallet", "Rent": "Rent", "rental.paymentBrand": "Payment Brand", "Resend to ally": "Resend to ally", "Successfully sent to the ally": "Successfully sent to the ally", "rental.carwahCommission": "Carwah Commission", "rental.updateButtonToUpdateBookingRequest": "Update Button To Update Booking Request", "rental.carDetails": "Car Details", "rental.carListingIdOrNumber": "Car ID", "Wallet balance": "Wallet balance", "rental.carColor": "Car color", "rental.carsetFeedbacks": "Car setFeedbacks", "rental.rentPerDay": "Rent Per Day", "rental.carPlateNumber": "Car Plate Number", "rental.insuranceCostPerDay": "Insurance Cost Per Day", "rental.listing": "Listing", "installments": "installments", "rental.bookingDetails": "Booking Details", "rental.main.bookingDetails": "Main Booking Details", "PENDING_EXTEND": "PENDING EXTEND", "REJECTED_EXTEND": "REJECTED EXTEND", "CANCELLED_AFTER_CONFIRM": "CANCELLED AFTER CONFIRM", "rental.allyDetails": "<PERSON>", "rental.allyName": "Ally Name", "rental.allyEmailAddress": " Email Address", "rental.allyPhoneNumber": "Phone Number", "rental.allyOfficeNumber": "Office Number", "rental.monthlyPrice": "Monthly Price", "rental.weeklyPrice": "Weekly Price", "rental.distanceByDay": "Available KM/day", "rental.distanceBetweenCarUser": "Number KM/Away", "customer.phone": "Customer Phone", "same.as.pickup.location": "Dropoff city is same as the pickup city", "total.rental.days": "Total rental days", "selectInsurance": "Please select isurance type", "find.cars": "Find available cars", "thisUserIsNotRegisteredYet": "This user is not registered yet", "select.car": "Select car", "paymetMethod": "Paymet Method", "day": "day", "pick_up_city_id": "Pickup City", "no_delivery": "No Delivery", "REJECTED": "Decline", "ar_insurance_name": " Insurance Name Ar", "en_insurance_name": "En Insurance Name", "ar_drop_off_branch_name ": "Drop Off Branch Name Ar", "en_drop_off_branch_name": " Drop Off Branch Name En", "ar_drop_off_city_name": "Drop Off City Name Ar", "en_drop_off_city_name": "Drop Off City Name En", "ar_pick_up_city_name": "ar_pick_up_city_name", "en_pick_up_city_name": "Pick_up City Name En", "en_branch_name": "Branch Name En", "car_branch_lng": "Car Branch Lng", "car_branch_lat": "car Branch Lat", "ar_drop_off_branch_name": "Drop Off Branch Name AR", "with_wallet": "With Wallet", "with_installment": "With Installment", "handover_lat": "Handover Lat", "handover_lng": "Handover Lng", "handover_address": "Handover Address", "handover_distance": "Handover Distance", "delivery_distance": "Delivery Distance", "is_unlimited": "Is unlimited", "ally_extra_services": "Ally Extra Services", "customer_booking_lat": "Customer Booking Lat", "customer_booking_lng": "Customer Booking Lng", "coupon_id": "Coupon ID", "branch_extra_services": "Branch Extra Services", "carName": "Car Name", "ar_branch_name": " Branch Name Ar", "user_id": "User ID", "common.timeline": "Timeline", "ToAge.label": "To Age", "ToAge.placeholder": "To Age", "Car Type": "Car Type", "Allowed Car Types per Age Category": "Allowed Car Types per Age Category", "addRow": "addRow", "Ally Settings": "<PERSON>", "From Age.label": "From Age", "From Age.placeholder": "From Age", "bookings.paidamount": "<PERSON><PERSON>", "success.create.user": "Customer added successfully", "packagedeletedsuccessfully": "Package Deleted Successfully", "highly_requested_packages": "Packages", "success.edit.user": "Customer edited successfully", "success.create.model": "Model added successfully", "success.edit.model": "<PERSON> edited successfully", "success.create.nonCustomerUser": "User added successfully", "success.edit.nonCustomerUser": "User edited successfully", "nationality": "Nationality", "Highly requested": "Highly requested", "Saved successfully": "Saved successfully", "package": "Package", "VISAimage": "VISA image ", "accepting only numbers & letters": "accepting only numbers & letters", "driverLicenseExpireAt": "Driver license Expiry Date - <PERSON><PERSON> ", "driverLicenseExpireAt-hijri": "Driver license Expiry Date - <PERSON><PERSON><PERSON>", "nid.label": "National ID", "ID Image": "ID Image", "it must be 10 digits not less or more than 10": "it must be 10 digits not less or more than 10", "ID must start with 1, and consist of 10 digits": "ID must start with 1, and consist of 10 digits", "ID must start with 2, and consist of 10 digits": "ID must start with 2, and consist of 10 digits", "accept max 15 digits": "Accept max 15 digits", "RecallGateway": "Recall Gateway", "accept max 15 character": "Accept max 15 character", "nationalIdVersion.label": "Version ", "resident": "Resident", "borderNumber.label": "Boarder No.", "accepting only numbers": "accepting only numbers", "accept 10 digits": "Accepting only Numbers", "passportNumber or borderNumber required": "Passport Number or Boarder Number Required", "borderNumber.placeholder": "Boarder No.", "gulf_citizen": "Gulf Citizen", "nationalIdVersion.placeholder": "Version ", "this.booking.can't.be.extended": "This Booking Can't Be Extended. Click to go back", "nationalIdExpireAt": "National ID Expiry Date - Gregor<PERSON> ", "nationalIdExpireAt-hijri": "National ID Expiry Date - Hi<PERSON><PERSON> ", "rental.nationalId": "National ID", "GulfIdExpireAt": "Gulf ID Expiry Date", "iqamaIdExpireAt": "Iqama Expiry Date", "passportNumber.label": "Passport number", "passportNumber.placeholder": "Passport number", "driverLicense.placeholder": "Driver license number", "driverLicense.label": "Driver license number", "managerName.label": "Manager Name", "customersetFeedbacks": "Customer setFeedbacks", "managersetFeedbacks": "Manager setFeedbacks", "licenseSelfieImage": "Driver license with selfie", "licenseFrontImage": "Upload driver license", "passportFrontImage": "Passport Front Image", "businessCard": "Business Card", "selectImage": "Select Image", "changeImage": "Change Image", "passportExpireAt": "Passport expiry date - <PERSON><PERSON> ", "passportExpireAt-hijri": "Passport expiry date - <PERSON><PERSON><PERSON> ", "usersProfileIsNotComplete": "Incomplete customer profile, Can't make a booking", "daily": "Daily", "monthly": "Monthly", "delivery": "Delivery", "deliveryLocation": "Delivery Location", "pleaseSelectSuitablePicupDate": "please select a suitable pick-up date", "aboutPrice": "About price", "aboutPrice.Basic": "Basic", "aboutPrice.PricePerDay": "Price per day", "aboutPrice.totalDays": "Total days ({days})", "aboutPrice.discount": "Discount ({discount}) - ({percentage})%", "aboutPrice.couponCode": "Coupon Code ({couponCode})", "aboutPrice.extraServices": "Extra Services", "Customertype": "Type", "Successful Bookings": "Successful Bookings", "aboutPrice.deliveryCost": "Car Delivery Fee", "aboutPrice.insurance": "Insurance ({insurance})", "aboutPrice.total": "Total", "aboutPrice.vat": "Vat {vat}%", "aboutPrice.+vat": " + Vat", "aboutPrice.grandTotal": "Grand Total", "price.sr": "{price} SR", "price.sr/day": "{price} SAR/Day", "two_ways": "Two way destination", "rent.month": "Rent/ 1months", "rent.week": "Rent/Week", "one_way": "One way destination", "profileImage": "Profile Image", "email-address": "Email Address", "maximumAllowedImageSizeError": "The max. allowed size is20 Mb.", "issueSavingProfileImage": "An error occured while saving profile image. please try again later.", "months.count": "Months", "change.setFeedbacks": "Change setFeedbacks", "rental.bookingType": "Booking type", "licenceImage": "Licence Image", "logo": "Logo", "dob": "Date of Birth (<PERSON><PERSON>)", "car.count": "Car numbers", "success.create.branch": "Branch created successfully", "success.edit.branch": "<PERSON> edited successfully", "deliverToAirport": "Airport branch", "fixedDeliveryFees": "Fixed Delivery Fees", "fixedDeliveryFees.label": "Fixed Delivery Fees", "fixedDeliveryFees.placeholder": "Fixed Delivery Fees", "canDelivery": "Can Delivery", "please.ensure.you.filled.all.time.slots": "Please fill all added time slots", "national.id.version.must.be.between.one.nine": "National ID allowed a number between 1 and 9", "selecting.company": "Selceting a company", "selecting.branch": "Selceting a branch", "InvoiceImage": "Invoice image", "grandTotal.label": "Grand total", "grandTotal.placeholder": "Grand total", "isUnlimited": "Is Unlimited", "isUnlimitedFree": "Is Unlimited free", "unlimitedFeePerDay": "Unlimited free per day", "unlimitedFeePerDay.car": "Unlimited free per day", "Insurance.cost.month": "Monthly Insurance Value", "unlimitedFeePerDay.label": "Unlimited free per day", "unlimitedFeePerDay.placeholder": "Unlimited free per day", "Id": "Id", "Rates": "Rates", "RateAr": "Rate in Arabic", "RateEn": "Rate in English", "ONLINE": "Online", "rental.bookingLocation": "Booking Location", "Pick-up Location": "Pick-up Location", "rental.bookingTiming": "Booking Timing", "Invoicing this booking will close the pending extensions are you sure you want to proceed? Please note that this action cannot be undone.": "Invoicing this booking will close the pending extensions are you sure you want to proceed? Please note that this action cannot be undone.", "Are you sure you want to invoice the booking? This action cannot be undone.": "Are you sure you want to invoice the booking? This action cannot be undone.", "rental.rentalCar": "Rental Car", "rental.enterphone": "Enter Customer Phone", "rate.label": "Rate value", "rental.extraServices": "Extra Services", "allyExtraServices": "Ally Extra Services", "branchExtraServices": "Branch Extra Services", "withoutTax": "Without Tax", "rental.taxValue": "Tax Value", "rental.priceBeforeTax": "Price Before Tax", "rental.totalBookingPrice": "Total Booking Price", "mr.manager": "Manager", "DOB.Gregor": "Date Of Birth - <PERSON><PERSON>", "DOB.Hijri": "Date Of Birth - <PERSON><PERSON><PERSON>", "ok": "Ok", "age": "Age", "year": "year", "sidebar.businessRequests": "Business Requests", "basic_member": "Basic member", "bronze_member": "Bronze member", "gold_member": "Gold member", "private_member": "Private member", "customerClass": "Customer Class", "sidebar.businessrequests": "Business Requests", "businessrequest": "Business Request", "Request ID": "Request ID", "Request ID.placeholder": "Request ID", "Customer": "Customer", "Car": "Car", "Car numbers": "Car numbers", "City": "City", "Duration in months": "Duration in months", "Expected pick up date": "Expected pick up date", "Insurance type": "Insurance type", "Additional notes": "Additional notes", "See More": "See More", "mobile": "Phone", "created_by": "Created By", "nationality_id": "Nationality ID", "visa_image": "visa Image", "login_attempt_at": "Login Attempt At", "offer": "Bid offer", "icon_url": "Icon Url", "Month": "Month", "bannerTimeLine": "Banner <PERSON> ", "reference_code": "Reference Code", "deliver_to_airport": "Delivery to Airport", "office_number": "Office Number", "vehicle_type_id": "Vehicle Type ID", "BranchTimeLine": "Branch TimeLine", "can_delivery": "Can Delivery", "profile_image": "Profile Image", "airport_id": "Airport ID", "cash_and_online": "cash_and_online", "Offer Data": "Offer Data", "blocking_allies": "Blocking Allies", "Max. is 9999999.99": "Max. is 9999999.99", "Max. is 999999.99": "Max. is 999999.99", "Max. is 99999": "Max. is 99999", "Max. is 999999": "Max. is 999999", "Please enter a valid value": "Please enter a valid value", "Please enter a valid value between 1 and 99999": "Please enter a valid value between 1 and 99999", "Please enter a valid value between 1 and 999999": "Please enter a valid value between 1 and 999999", "Offer Price": "Offer Price", "Car Insurance": "Car Insurance", "Standard": "Standard", "value.label": "Value", "Extension request  is confirmed successfully": "Extension request  is confirmed successfully", "Extension request": "Extension request", "Request.No": "Request No", "Extension.Duration.label": "Extension Duration", "Request.setFeedbacks": "Request setFeedbacks", "Full": "Full", "SAR / Month": "SAR / Month", "Insurance value": "Insurance value", "No.of.total.usages": "No. of total usages", "No.of.users": "No. of users", "Coupon.sales": "Coupon sales", "Monthly Insurance value": "Monthly Insurance value", "Kilometer allowed per month": "Kilometer allowed per month", "Additional distance cost": "Additional distance cost", "Please enter.offerPrice": "Please enter offer price", "Please enter.carInsuranceStandard": "Please enter Insurance value", "Please enter.carInsuranceFull": "Please enter Monthly Insurance value", "Please enter.kilometerPerMonth": "Please enter Kilometer allowed per month", "Please enter.additionalKilometer": "Please enter Additional distance cost", "Please enter.allyCompanyId": "Please choose an ally", "Please choose at leaset one insurance type": "Please choose at leaset one insurance type", "Bid offer sent successfully": "Offer has been sent successfully", "request.details": "Business Request Details", "offer.details": "Offer Details", "Offer date": "Offer date", "value": "Value", "max.is": "Max is", "Partial.Refund": "Partial Refund", "Offer ID": "Offer ID", "EditRequest": "Edit Request", "AddRequest": "Create request", "numberOfCars": "Car numbers", "Type here": "Notes", "Min. 1& Max. 60": "Min. 1& Max. 60", "requestCreated": "Request has been created", "selectAlly": "Please select Ally", "plan": "Plan", "offerData": "Offer Data", "rental.notes": "Rental Notes", "month": "Month", "requestUpdated": "Request Updated", "business_rentals": "Business Rentals", "businessBookings": "Business Bookings", "sidebar.businessBookings": "Business Bookings", "bookings.list.header.billingAmountMonth": "Monthly Price", "Show on main page": "Show on main page", "Blocking setFeedbacks": "Blocking setFeedbacks", "Blocking reason": "Blocking reason", "Start time of the shift must be before the end time": "Start time of the shift must be before the end time", "Time overlapping is not allowed for different shifts on a day": "Time overlapping is not allowed for different shifts on a day", "Delivery Payment Method": "Delivery Payment Method", "Delivery Grace Time": "Delivery Grace <PERSON>", "Hrs.": "Hrs.", "can_handover_in_anther_city": "Can Handover Inanther City", "Payment setFeedbacks": "Payment setFeedbacks", "Not Paid": "Not Paid", "phone_number": "Phone Number", "ally_class": "Ally Class", "commercial_registration_image": "Commercial Registration Image", "manager_name": "Manager Name", "Paid": "Paid", "Refunded": "Refunded", "Filter": "Filter", "deleted": "deleted", "Daily insurance value": "Daily insurance value", "Monthly insurance value": "Monthly insurance value", "makeModelVersion": "Make, Model, Year", "please enter close reason": "Please enter close reason", "please choose atleast one close reason": "Please choose atleast one close reason", "businessBookings.list.pickup_date_time": "Pickup date time", "businessBookings.list.dropoff_date_time": "Dropoff date time", "business_requests": "Business Requests", "Select a pickup date": "Select a pickup date", "Please enter a daily or monthly value!": "Please enter a daily or monthly insurance value!", "unblocked": "Unblocked", "Manage Extensions": "Manage Extensions", "Extension Requests": "Extension Requests", "Request No.": "Request No.", "New return date": "New return date", "New_return_Time": "New return time", "Extension Days": "Extension Days", "Value": "Value", "Deleted": "Deleted", "Decline.reason": "Decline reason", "Ally.Decline.reason": "Ally Decline reasons", "Request setFeedbacks": "Request setFeedbacks", "Extension request is rejected successfully": "Extension request is rejected successfully", "Extension duration": "Extension duration", "Grand Total +VAT": "Grand Total +VAT", "isExtendFixedPrice": "Extend - rental fixed price", "Airports": "Airports", "Choose Airport": "Please choose an Airport", "select.Pickup branch": "Selecting Pickup branch", "select.Dropoff branch": "Selecting Dropoff branch", "handover_branch_price": "Change Handover service fees", "shifts_grace_validation": "The shift must be greater than the grace hours", "closing.extension": "Closing this booking will close the pending extensions as well", "Carwah Business": "Carwah Business", "Can't delete this customer, as he has an active rental!": "Can't delete this customer, as he has an active rental !", "Allies": "Allies", "Amount": "Amount", "Due.Date": "Due Date", "Cars": "Cars", "sidebar.packages": "Packages", "Roles & Permissions": "Roles & Permissions", "Settings": "Settings", "action.setFeedbacks": "Action setFeedbacks", "createdat": "Created At", "Integrated Ally's Contract": "Integrated Ally's Contract", "refund.amount": "Refund Amount", "operation.datetime": "Operation datetime", "operationType": "Operation Type", "Weekly Package rate should be smaller than or equal the previous one": "Weekly Package rate should be smaller than or equal the previous one", "Monthly Package rate should be smaller than or equal the previous one": "Monthly Package rate should be smaller than or equal the previous one", "acrissCode.placeholder": "Acriss Code", "En Plate Char": "En Plate Characters", "acriss_code": "Acriss Code", "Ar Plate Char": "Ar Plate Characters", "Verified via Yakeen": "Verified via Yakeen", "UnVerified via Yakeen": "UnVerified via Yakeen", "Yakeen verification": "Yakeen verification", "Installments.booking": "Installments", "share via": "Share Via", "something went wrong": "Something Went Wrong", "Shared Successfully": "Shared Successfully", "sidebar.agencies": "Agencies", "AddSuccessfully": "Added Successfully", "sidebar.Agencies": "Agencies List", "sidebar.AgenciesList": "Agencies", "sidebar.profile": "Profile", "sidebar.usersList": "Users List", "agency_admin": "Admin", "agency_customer_service": "Customer Service", "agency_super_admin": "Super Admin", "payment.link": "Payment Link", "generate.payment.link": "Generate Payment Link", "copy.link": "Copy Link", "copied.to.clipboard": "Link copied to clipboard", "copy.failed": "Failed to copy link", "components.total.results": "Total Results: {count}", "Choose a payment method": "Choose a payment method", "Mada": "<PERSON><PERSON>", "Credit Card": "Credit Card", "Pay with Mada card": "Pay with Mada card", "Pay with Visa or Mastercard": "Pay with Visa or Mastercard", "3 Payments interest free": "3 Payments interest free", "Wallet Balance": "Wallet Balance", "Available Balance": "Available Balance", "Pay with wallet": "Pay with wallet", "Use available balance": "Use available balance", "Full amount will be paid from wallet": "Full amount will be paid from wallet", "Wallet will cover {amount} SAR, remaining {remaining} SAR will be charged": "Wallet will cover {amount} SAR, remaining {remaining} SAR will be charged", "Proceed to Payment": "Proceed to Payment", "Agency is deactivated. Payment is not allowed.": "Agency is deactivated. Payment is not allowed.", "Payment": "Payment", "Selected Payment Method": "Selected Payment Method", "Using {amount} SAR from wallet": "Using {amount} SAR from wallet", "Processing payment...": "Processing payment...", "Complete Payment": "Complete Payment", "Payment completed successfully!": "Payment completed successfully!", "Payment failed": "Payment failed", "Payment is being processed...": "Payment is being processed...", "Waiting for payment confirmation...": "Waiting for payment confirmation...", "Error:": "Error:", "Your payment has been processed successfully. The booking status will be updated shortly.": "Your payment has been processed successfully. The booking status will be updated shortly.", "Please wait while we process your payment. This may take a few moments.": "Please wait while we process your payment. This may take a few moments.", "Try Again": "Try Again", "Done": "Done", "Close": "Close", "Cancel": "Cancel", "Failed to create Tamara checkout": "Failed to create Tamara checkout", "Tamara payment failed": "Tamara payment failed", "Wallet payment failed": "Wallet payment failed", "Failed to get checkout ID": "Failed to get checkout ID", "Payment initialization failed": "Payment initialization failed", "Failed to load payment widget": "Failed to load payment widget", "Payment completed successfully": "Payment completed successfully", "Check Status": "Check Status", "Pay Now": "Pay Now", "(Through Agency {agencyName})": "(Through Agency {agencyName})", "Dear customer.. the payment process was unsuccessful, try to pay again": "Dear customer.. the payment process was unsuccessful, try to pay again", "Dear customer... Your payment process is currently pending by Hyperpay. The payment status will be updated once the payment is completed.": "Dear customer... Your payment process is currently pending by Hyperpay. The payment status will be updated once the payment is completed.", "Pending payment": "Pending payment", "Payment has been received": "Payment has been received", "Loading...": "Loading...", "Loading payment form...": "Loading payment form...", "Powered by Tamara": "Powered by <PERSON>", "Tamara not available": "Tamara not available", "Amount must be between {min} and {max} SAR": "Amount must be between {min} and {max} SAR", "Pay Extension": "Pay Extension", "Pay Installment": "Pay Installment", "Extension payment completed successfully": "Extension payment completed successfully", "Installment payment completed successfully": "Installment payment completed successfully", "Extension payment failed": "Extension payment failed", "Installment payment failed": "Installment payment failed", "Paid by": "Paid by", "(Through Agency)": "(Through Agency)"}