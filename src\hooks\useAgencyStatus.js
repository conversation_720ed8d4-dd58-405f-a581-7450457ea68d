import { useQuery } from "@apollo/client";
import { useSelector } from "react-redux";
import { Agency } from "../gql/queries/Agencies.gql";

/**
 * Hook to check the current agency's status
 * @returns {Object} - { isAgencyDeactivated, agencyData, loading, error }
 */
const useAgencyStatus = () => {
  const { user } = useSelector((state) => state.authUser) || {};
  const agencyId = user?.agency_id;

  const { data: agencyData, loading, error } = useQuery(Agency, {
    skip: !agencyId,
    variables: { id: agencyId },
    fetchPolicy: "cache-first",
    errorPolicy: "all",
  });

  const agency = agencyData?.agency;
  const isAgencyDeactivated = agency ? !agency.isActive : false;

  return {
    isAgencyDeactivated,
    agencyData: agency,
    loading,
    error,
    agencyId,
  };
};

export default useAgencyStatus;
