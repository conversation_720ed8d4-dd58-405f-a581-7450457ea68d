.payment-button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
  
  .material-icons {
    font-size: 18px;
  }
}

// Size variations
.payment-button.btn-sm {
  padding: 6px 12px;
  font-size: 13px;
  
  .material-icons {
    font-size: 16px;
  }
}

.payment-button.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
  
  .material-icons {
    font-size: 20px;
  }
}

// Color variations
.payment-button.btn-success {
  background-color: #28a745;
  border-color: #28a745;
  
  &:hover:not(:disabled) {
    background-color: #218838;
    border-color: #1e7e34;
  }
}

.payment-button.btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
  
  &:hover:not(:disabled) {
    background-color: #e0a800;
    border-color: #d39e00;
  }
}

.payment-button.btn-info {
  background-color: #17a2b8;
  border-color: #17a2b8;
  
  &:hover:not(:disabled) {
    background-color: #138496;
    border-color: #117a8b;
  }
}

// RTL Support
[dir="rtl"] {
  .payment-button {
    flex-direction: row-reverse;
  }
}

// Loading state
.payment-button.loading {
  position: relative;
  color: transparent;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// Responsive design
@media (max-width: 768px) {
  .payment-button {
    width: 100%;
    justify-content: center;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .payment-button {
    &:disabled {
      opacity: 0.5;
    }
  }
}
