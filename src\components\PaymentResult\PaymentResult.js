import React, { useEffect, useState } from "react";
import { useLocation, useHistory } from "react-router-dom";
import { useIntl } from "react-intl";
import { useLazyQuery } from "@apollo/client";
import { NotificationManager } from "react-notifications";
import { Helmet } from "react-helmet";
import PageTitleBar from "components/PageTitleBar/PageTitleBar";
import IntlMessages from "util/IntlMessages";
import PaymentStatusModal from "../PaymentFlow/PaymentStatusModal";
import {
  PAYMENT_STATUS_QUERY,
  INSTALLMENT_PAYMENT_STATUS_QUERY,
  EXTENSION_PAYMENT_STATUS_QUERY,
} from "../PaymentFlow/graphql";
import "./PaymentResult.scss";

const PaymentResult = () => {
  const { formatMessage } = useIntl();
  const location = useLocation();
  const history = useHistory();
  const [checkoutId, setCheckoutId] = useState(null);
  const [paymentStatus, setPaymentStatus] = useState("checking");
  const [error, setError] = useState(null);

  // Extract URL parameters
  const urlParams = new URLSearchParams(location.search);
  const id = urlParams.get("id");
  const resourcePath = urlParams.get("resourcePath");

  // Get stored payment context from sessionStorage
  const paymentContext = JSON.parse(sessionStorage.getItem("paymentContext") || "{}");
  const { rentalId, extensionId, isInstallment } = paymentContext;

  // Debug logging
  console.log("PaymentResult Debug:", {
    urlId: id,
    resourcePath,
    paymentContext,
    rentalId,
    extensionId,
    isInstallment,
  });

  // Choose the appropriate query based on payment type
  const statusQuery = extensionId
    ? EXTENSION_PAYMENT_STATUS_QUERY
    : isInstallment
    ? INSTALLMENT_PAYMENT_STATUS_QUERY
    : PAYMENT_STATUS_QUERY;

  const [checkPaymentStatus, { loading }] = useLazyQuery(statusQuery, {
    errorPolicy: "all",
  });

  useEffect(() => {
    // Extract checkout ID from the URL parameters
    if (id) {
      setCheckoutId(id);
    }
  }, [id]);

  useEffect(() => {
    const checkStatus = async () => {
      // Use checkout ID from URL if available, otherwise from sessionStorage
      const currentCheckoutId = checkoutId || paymentContext.checkoutId;

      if (!currentCheckoutId) {
        setError(formatMessage({ id: "No checkout ID found" }));
        setPaymentStatus("failed");
        return;
      }

      if (!rentalId && !extensionId) {
        setError(
          formatMessage({
            id: "Payment session expired. Please check your payment status in the dashboard.",
          }),
        );
        setPaymentStatus("failed");
        return;
      }

      try {
        const variables = extensionId
          ? { checkoutId: currentCheckoutId, rentalExtensionId: extensionId }
          : { checkoutId: currentCheckoutId, rentalId };

        const result = await checkPaymentStatus({ variables });

        let status;
        let errors;

        if (extensionId) {
          status = result.data?.extensionGetPaymentStatus?.status;
          errors = result.data?.extensionGetPaymentStatus?.errors;
        } else if (isInstallment) {
          status = result.data?.installmentGetPaymentStatus?.status;
          errors = result.data?.installmentGetPaymentStatus?.errors;
        } else {
          status = result.data?.getPaymentStatus?.status;
          errors = result.data?.getPaymentStatus?.errors;
        }

        setPaymentStatus(status || "failed");

        if (status === "paid") {
          // Clear payment context
          sessionStorage.removeItem("paymentContext");
          // Redirect to booking page after 3 seconds
          const bookingId = rentalId || paymentContext.rentalId;
          setTimeout(() => {
            if (bookingId) {
              history.push(`/cw/dashboard/bookings/${bookingId}`);
            } else {
              history.push("/cw");
            }
          }, 3000);
        } else if (status === "failed" && errors?.length > 0) {
          setError(errors[0]);
          NotificationManager.error(errors[0]);
        } else if (!status) {
          setError(formatMessage({ id: "Unable to verify payment status" }));
        }
      } catch (err) {
        console.error("Payment status check error:", err);
        setError(err.message || formatMessage({ id: "Payment verification failed" }));
        setPaymentStatus("failed");
      }
    };

    if (checkoutId || paymentContext.checkoutId) {
      checkStatus();
    }
  }, [
    checkoutId,
    paymentContext.checkoutId,
    rentalId,
    extensionId,
    isInstallment,
    checkPaymentStatus,
    formatMessage,
    history,
  ]);

  const handleRetry = () => {
    sessionStorage.removeItem("paymentContext");
    history.goBack();
  };

  const handleClose = () => {
    sessionStorage.removeItem("paymentContext");
    const bookingId = rentalId || paymentContext.rentalId;
    if (bookingId) {
      history.push(`/cw/dashboard/bookings/${bookingId}`);
    } else {
      history.push("/cw");
    }
  };

  const handleManualCheck = async () => {
    const currentCheckoutId = checkoutId || paymentContext.checkoutId;

    if (currentCheckoutId) {
      const checkStatus = async () => {
        try {
          const variables = extensionId
            ? { checkoutId: currentCheckoutId, rentalExtensionId: extensionId }
            : { checkoutId: currentCheckoutId, rentalId };

          const result = await checkPaymentStatus({ variables });

          let status;
          let errors;

          if (extensionId) {
            status = result.data?.extensionGetPaymentStatus?.status;
            errors = result.data?.extensionGetPaymentStatus?.errors;
          } else if (isInstallment) {
            status = result.data?.installmentGetPaymentStatus?.status;
            errors = result.data?.installmentGetPaymentStatus?.errors;
          } else {
            status = result.data?.getPaymentStatus?.status;
            errors = result.data?.getPaymentStatus?.errors;
          }

          setPaymentStatus(status || "failed");

          if (status === "paid") {
            sessionStorage.removeItem("paymentContext");
            const bookingId = rentalId || paymentContext.rentalId;
            setTimeout(() => {
              if (bookingId) {
                history.push(`/cw/dashboard/bookings/${bookingId}`);
              } else {
                history.push("/cw");
              }
            }, 3000);
          } else if (status === "failed" && errors?.length > 0) {
            setError(errors[0]);
            NotificationManager.error(errors[0]);
          }
        } catch (err) {
          console.error("Manual payment status check error:", err);
          setError(err.message || formatMessage({ id: "Payment verification failed" }));
          setPaymentStatus("failed");
        }
      };

      await checkStatus();
    }
  };

  // Check if we have no payment information at all
  useEffect(() => {
    if (!paymentContext.rentalId && !paymentContext.extensionId && !id) {
      setPaymentStatus("failed");
      setError(
        formatMessage({
          id: "No payment information found. Please return to the dashboard.",
        }),
      );
    }
  }, [paymentContext.rentalId, paymentContext.extensionId, id, formatMessage]);

  return (
    <div className="ecom-dashboard-wrapper">
      <Helmet>
        <title>{formatMessage({ id: "Payment Result" })}</title>
        <meta name="description" content="Payment Result Page" />
      </Helmet>
      <PageTitleBar
        title={<IntlMessages id="Payment Result" />}
        enableBreadCrumb
        match={location}
      />
      <div className="payment-result-container">
        <PaymentStatusModal
          status={paymentStatus}
          error={error}
          onRetry={handleRetry}
          onClose={handleClose}
          onManualCheck={handleManualCheck}
          loading={loading}
        />
      </div>
    </div>
  );
};

export default PaymentResult;
