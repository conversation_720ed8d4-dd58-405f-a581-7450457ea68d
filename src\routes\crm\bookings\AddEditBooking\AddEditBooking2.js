/* eslint-disable prettier/prettier */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/button-has-type */
/* eslint-disable jsx-a11y/control-has-associated-label */
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
/* eslint-disable spaced-comment */
/* eslint-disable radix */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable no-restricted-globals */
/* eslint-disable no-undefined */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable prefer-const */
/* eslint-disable no-nested-ternary */
/* eslint-disable eqeqeq */
import React, { useState, useRef, useMemo, useEffect } from "react";
import { useHistory, useParams } from "react-router-dom";
import { FormattedMessage, useIntl } from "react-intl";
import { useQuery, useMutation, useLazyQuery } from "@apollo/client";
import { AllAreas } from "gql/queries/Areas.queries.gql";
import {
  CreateBooking,
  EditBooking,
  RejectRentalDateExtensionRequest,
} from "gql/mutations/Rental.mutations.gql";
import { ResendRentalIntegration } from "gql/mutations/ResendRental.gql";
import { CustomerDataDisplay } from "components/CustomerDataDisplay";
import AsyncLoader from "components/AutoComplete/AsyncLoader";
import { Autocomplete } from "@material-ui/lab";
import { bookingsTypes, MonthsOfRent,DeliveryTypes } from "constants/constants";
import moment from "moment";
import Map from "components/Map/MapWithSearch";
import { NotificationManager } from "react-notifications";
import "moment/locale/en-au";
import AppBar from "@material-ui/core/AppBar";
import { Tab, Tabs, TabList, TabPanel } from "react-tabs";
import "react-tabs/style/react-tabs.css";
import Checkbox from "@material-ui/core/Checkbox";

// import { Tab, Tabs, TabList, TabPanel } from "react-tabs";

import {
  CircularProgress,
  FormControlLabel,
  Radio,
  FormControl,
  InputLabel,
  TextField,
  Tooltip,
} from "@material-ui/core";

import FormControlLabelContainer from "components/shared/FormControlLabelContainer";
import RadioGroupContainer from "components/shared/containers/RadioGroupContainer";
import { DateTimePickerCustom } from "components/DateTimePickerCustom";
import { GetCarProfile, GetAllAvailableCars } from "gql/queries/Cars.queries.gql";
import { GetArea } from "gql/queries/GetArea.gql";
import {
  GetRentPrice,
  GetRentalDetailsQuery,
  GetBookingsQuery,
} from "gql/queries/Rental.queries.gql";
import Box from "@material-ui/core/Box";
import { GetCustomerDetailsQuery } from "gql/queries/Users.queries.gql";
import FullPageLogoLoader from "components/shared/FullPageLogoLoader";
import Select from "react-select";
import { AvailableBranches } from "gql/queries/AllBranches.gql";
import { AvailableAllyCompanies } from "gql/queries/AllCompanies.gql";
import { UserWallet } from "gql/queries/CustomerWalletBalance.gql";
import { Profile } from "gql/queries/AdminProfile.gql";
import swal from "sweetalert";
import { useSnackbar } from "notistack";
import GettingCustomerDetails from "./GettingCustomerDetails";
import BookingPriceSummary2 from "../booking-details/BookingPriceSummary2";

import styles from "./_style.module.scss";
import InstallmentsTable from "./InstallmentsTable";
import useEffects2 from "./useEffects2";
import Modals from "./Modals";
import Header from "./Header";
import FursanVerification from "./FursanVerification";
import { ReportProblem } from "@material-ui/icons";
import CustomTextField from "components/Input/CustomTextField";

import { CarCouponAvailability } from "gql/queries/CarCouponAvailable.gql";
import AreasDropDown from "components/DropDowns/AreasDropDown";
import { CitiesDropDown } from "components/DropDowns";
import BranchesDropdownForBooking from "components/DropDowns/BranchesDropdownForBooking";
import CarsDropDownForBooking from "components/DropDowns/CarsDropDownForBooking";
import ExtraServiceComponent from "./ExtraServiceComponent";
import CouponDiscountComponent from "./CouponDiscountComponent";
import InsuranceDropDown from "./InsuranceDropDown";
import DeliveryComponent from "./DeliveryComponent";
//Custom Styling
const customStyles = {
  menuPortal: (provided) => ({ ...provided, zIndex: 9999 }),
  menu: (provided) => ({ ...provided, zIndex: 9999 }),
};

function AddEditBooking2() {
  //Utilities
  const { locale, formatMessage, messages } = useIntl();
  const { ally_id } = JSON.parse(localStorage.getItem("user_data"));
  const Noteref = useRef("");
  const copounref = useRef("");
  const suggestedPricePerDayRef = useRef("");
  const { enqueueSnackbar } = useSnackbar();

  //State
  const { bookingId } = useParams();
  const history = useHistory();
  const [allCompanies, setAllCompanies] = useState([]);
  const [customerDetails, setCustomerDetails] = useState(null);
  const [customerId, setCustomerId] = useState(null);
  const [isPickSameReturn, setIsPickSameReturn] = useState(true);
  const [pickUpCity, setPickUpCity] = useState();
  const [dropOffCity, setDropOffCity] = useState();
  const [pickUpDate, setPickUpDate] = useState(moment().add(2, "hour"));
  const [dropOffDate, setDropOffDate] = useState(moment().add(3, "day").add(2, "hour"));
  const [selectedCar, setSelectedCar] = useState();
  const [bookingType, setBookingType] = useState("daily");
  const [deliverLat, setDeliverLat] = useState();
  const [deliverLng, setDeliverLng] = useState();
  const [insuranceId, setInsuranceId] = useState();
  const [paymentMethod, setPaymentMethod] = useState("CASH");
  // const [suggestedPricePerDay, setSuggestedPricePerDay] = useState("");
  const [deliverAddress, setDeliverAddress] = useState("");
  const [editDatedReady, setEditDatedReady] = useState(false);
  const [months, setMonths] = useState("1");
  const [monthTime, setMonthTime] = useState();
  const [changed, setChanged] = useState(false);
  const [ready, setReady] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [selectedBranch, setSelectedBranch] = useState(null);
  const [selectedDropoffBranch, setSelectedDropoffBranch] = useState(null);
  const [availableCarsCollection, setAvailableCarsCollection] = useState([]);
  const [avaiableCarsDD, setAvaiableCarsDD] = useState([]);
  const [extraServices, setExtraServices] = useState({});
  const [allyExtraServicesIds, setAllyExtraServicesIds] = useState([]);
  const [branchExtraServicesIds, setBranchExtraServicesIds] = useState([]);
  // const [note, setNote] = useState();
  const [extensionModalOpen, setIsExtensionModalOpen] = useState(false);
  const [distanceCarUser, setDistanceCarUser] = useState();
  const [carPlans, setCarPlans] = useState([]);
  const [plan, setPlan] = useState();
  const [deliveryPrice, setDeliveryPrice] = useState();
  const [handoverprice, setHandOverPrice] = useState(null);
  const [handoverChecked, setHandOverChecked] = useState();
  const [FursanChecked, setFusranChecked] = useState();
  const [OpenRejectionModal, setOpenRejectionModal] = useState(false);
  const [handoverlat, setHandoverLat] = useState();
  const [carChanged, setCarChanged] = useState(false);
  const [handoverTobranch,setHandoverToBranch]=useState(false)

  const [handovderLng, setHandoverLng] = useState();
  const [clicked, setClicked] = useState(false);
  const [copounId, setCopounId] = useState();
  const [unLimited, setunLimited] = useState(false);
  const [dimmed, setDimmed] = useState(false);
  const [isDelivery, setIsDelivery] = useState(false);
  const [withInstallment, setwithInstallment] = useState(false);
  const [copounCode, setCouponCode] = useState();
  const [couponAvailabilty, setCouponAvailability] = useState();
  const [handoverToSamePickup,setHandoverToSamePickup]=useState(false)
  //Variables

  //GQL
  const [createBookingMutation, { loading: creatingRental }] = useMutation(CreateBooking);
  const [EditBookingMutation, { loading: editingRental }] = useMutation(EditBooking);
  const [resendRental] = useMutation(ResendRentalIntegration);
  const { data: userInfo } = useQuery(Profile);

  const { refetch: refetchAllBookings } = useQuery(GetBookingsQuery, {
    skip: true,
  });

  const { data: rentalDetails, refetch: refetchBooking } = useQuery(GetRentalDetailsQuery, {
    skip: !bookingId,
    variables: { id: bookingId },
    fetchPolicy: "network-only",
    errorPolicy: "all",
    onError(error) {
      NotificationManager.error(error.message);
    },
  });
  const [getAreasQuery, { data: AreasRes, loading: gettingAreas }] = useLazyQuery(AllAreas);
  const [getCarCouponAvailable] = useLazyQuery(CarCouponAvailability);

  // Customer Data Request by id for edit mode
  const { data: customerDetailsRes } = useQuery(
    GetCustomerDetailsQuery,

    {
      skip: !rentalDetails?.rentalDetails,
      variables: { id: rentalDetails?.rentalDetails?.userId },
      errorPolicy: "all",
      onError(error) {
        NotificationManager.error(error.message);
      },
    },
  );
  const { data: walletBalance } = useQuery(UserWallet, {
    skip: !rentalDetails?.rentalDetails,
    variables: { userId: +rentalDetails?.rentalDetails?.userId },
  });

  const [getBranches, { data: branches }] = useLazyQuery(AvailableBranches, {
    fetchPolicy: "no-cache",
  });

  // Car Details Request
  const [getCarDetails, { data: carDetailsRes }] = useLazyQuery(GetCarProfile, {
    skip: !rentalDetails?.rentalDetails,
    variables: { id: rentalDetails?.rentalDetails?.carId },
  });

  const {
    data: BookingPriceRes,
    loading: calculatingPrice,
    refetch: recalculateRentPrice,
  } = useQuery(GetRentPrice, {
    skip:
      (!insuranceId && bookingType != "rent-to-own") ||
      !selectedCar ||
      (bookingId && !rentalDetails) ||
      (bookingType == "rent-to-own" && !plan?.id) || !changed,
    // !changed,
    errorPolicy: "all",
    onError(error) {
      if (error.message.includes("can not be applied")) {
        NotificationManager.error(
          <FormattedMessage id="Installment payments are not permitted for bookings with a duration less than 60 days or multiple of 30 days" />,
        );
      } else {
        NotificationManager.error(error.message);
      }
      setDimmed(true);
    },
    onCompleted: () => {
      setDimmed(false);
    },
    variables: {
      carId: selectedCar?.value,
      isCarChanged: carChanged,
      isUnlimited: unLimited,
      paymentMethod,
      deliveryPrice: isDelivery ? deliveryPrice : undefined,
      handoverPrice:
        handoverTobranch ||
        (handoverChecked) ||
        rentalDetails?.rentalDetails?.branchId !== rentalDetails?.rentalDetails?.dropOffBranchId
        || handoverToSamePickup
          ? handoverprice
          : undefined,
      ownCarPlanId: bookingType == "rent-to-own" ? (plan?.id ? plan?.id : undefined) : undefined,
      handoverBranch: handoverTobranch
        ? selectedDropoffBranch?.value || selectedDropoffBranch
        : undefined,
      deliverLat,
      deliverLng,
      couponId: copounId ? +copounId : undefined,

      deliveryType:
        isDelivery && (handoverToSamePickup) && !handoverTobranch
          ? "two_ways"
          : isDelivery && !handoverToSamePickup
          ? "one_way"
          : "no_delivery",
      dropOffDate:
        bookingType == "rent-to-own"
          ? undefined
          : moment(dropOffDate).locale("en").format("DD/MM/YYYY"),
      dropOffTime:
        bookingType == "rent-to-own"
          ? undefined
          : `${moment(dropOffDate).locale("en").format("HH:mm")}:00`,
      insuranceId: +insuranceId || undefined,
      pickUpDate: moment(pickUpDate?._id || pickUpDate)
        .locale("en")
        .format("DD/MM/YYYY"),
      pickUpTime: `${moment(pickUpDate?._id || pickUpDate)
        .locale("en")
        .format("HH:mm")}:00`,
      allyExtraServices: allyExtraServicesIds?.length ? [...new Set(allyExtraServicesIds)] : null,
      branchExtraServices: branchExtraServicesIds?.length
        ? [...new Set(branchExtraServicesIds)]
        : null ||
          rentalDetails?.rentalDetails?.rentalExtraServices
            ?.filter((s) => s.extraServiceType === "branch")
            .map((i) => i.id),
      usedPrice: !changed ? rentalDetails?.rentalDetails?.pricePerDay : undefined,
      // withWallet: !!rentalDetails?.rentalDetails?.walletTransactions,
      walletPaidAmount: rentalDetails?.rentalDetails?.walletTransactions?.amount
        ? rentalDetails?.rentalDetails?.walletTransactions?.amount
        : undefined,
      suggestedPrice: rentalDetails?.rentalDetails?.suggestedPrice || undefined,
      isEdit: !!bookingId,
      rentalId: bookingId || undefined,
      payWithInstallments:
        bookingType != "rent-to-own" &&
        (withInstallment || Boolean(rentalDetails?.rentalDetails?.installments?.length)),
    },
  });

  // recalculateRentPrice();

  const isRentalInstallment = BookingPriceRes?.aboutRentPrice?.installmentsBreakdown?.length;
  const isPaidInstallment = BookingPriceRes?.aboutRentPrice?.installmentsBreakdown?.find(
    (i) => i.status === "paid",
  );
  // Request => Available Cars
  const requestCarsvariables = {
    pickStartDate:
      rentalDetails?.rentalDetails && bookingId
        ? moment(pickUpDate?._id || pickUpDate)
            .locale("en")
            .format("DD/MM/YYYY")
        : moment(pickUpDate?._id || pickUpDate)
            .locale("en")
            .format("DD/MM/YYYY"),

    pickEndDate:
      moment(dropOffDate?._id || dropOffDate)
        .locale("en")
        .diff(moment(), "days") < 0
        ? null
        : dropOffDate?._id
        ? moment(dropOffDate?._id || dropOffDate)
            .locale("en")
            .format("DD/MM/YYYY")
        : undefined,
    pickUpLocationId: +pickUpCity?.id,
    dropOffLocationId: isPickSameReturn ? +pickUpCity?.id : +dropOffCity?.id,
    isActive: true,
  };
  const allBranches =
    branches?.availableBranches.collection.map((branch) => ({
      value: branch?.id,
      label: branch?.[`${locale}Name`],
      canHandover: branch?.canHandover,
      canDelivery: branch?.canDelivery,
      branchDeliveryPrices: [...branch?.branchDeliveryPrices],
    })) || [];

  const { data: allyCompanies, loading: loadingAllyCompanies } = useQuery(AvailableAllyCompanies, {
    variables: {
      limit: 1000,
      isRentToOwn: bookingType == "rent-to-own" ? true : undefined,
      pickupCityId: handoverTobranch
        ? +pickUpCity?.id || +rentalDetails?.rentalDetails?.pickUpCityId
        : undefined,
      dropoffCityId: handoverTobranch
        ? +dropOffCity?.id || +rentalDetails?.rentalDetails?.dropOffCityId
        : undefined,
      cityId: !handoverTobranch
        ? +pickUpCity?.id || +rentalDetails?.rentalDetails?.pickUpCityId
        : undefined,
        canDelivery:isDelivery,
        canHandover:handoverTobranch,
      pickStartDate:
        rentalDetails?.rentalDetails && bookingId
          ? moment(pickUpDate?._id || pickUpDate)
              .locale("en")
              .format("DD/MM/YYYY")
          : moment(pickUpDate?._id || pickUpDate)
              .locale("en")
              .format("DD/MM/YYYY"),
    },
  });
  const [getCars, { data: CarsRes, loading: gettingCars }] = useLazyQuery(GetAllAvailableCars, {
    fetchPolicy: "no-cache",
  });
  const [getArea, { data: area }] = useLazyQuery(GetArea);
  const [mapChange, setMapChange] = useState(false);
  const [opneTimeLineModal, setOpenTimeLineModal] = useState(false);
  const [FursanVerified, setFursanVerified] = useState();
  const [rejectRentalDateExtensionRequest] = useMutation(RejectRentalDateExtensionRequest);
  const [isopen, setIsOpen] = useState();
  //Functions
  const clearBranchSelection = () => {
    setSelectedBranch(null);
    setInsuranceId(null);
    setSelectedDropoffBranch(null);
    setAllyExtraServicesIds([]);
    setBranchExtraServicesIds([]);
  };

  //LifeCycle
  useEffects2({
    refetchBooking,
    setFusranChecked,
    isDelivery,
    AreasRes,
    getCarDetails,
    getAreasQuery,
    allyCompanies,
    rentalDetails,
    locale,
    setAllCompanies,
    handoverToSamePickup,
    area,
    handoverTobranch,
    changed,
    setPickUpCity,
    CarsRes,
    setAvailableCarsCollection,
    setAvaiableCarsDD,
    carName,
    carDetailsRes,
    bookingType,
    pickUpDate,
    dropOffDate,
    setMonths,
    setMonthTime,
    setDropOffDate,
    setDropOffCity,
    setSelectedDropoffBranch,
    monthTime,
    ready,
    editDatedReady,
    setDeliveryPrice,
    setHandOverPrice,
    setInsuranceId,
    setHandOverChecked,
    setPlan,
    setCustomerId,
    setCopounId,
    copounref,
    setCouponCode,
    setunLimited,
    dropOffCity,
    pickUpCity,
    isPickSameReturn,
    selectedDropoffBranch,
    suggestedPricePerDayRef,
    setBookingType,
    setEditDatedReady,
    setPickUpDate,
    setPaymentMethod,
    setReady,
    setDistanceCarUser,
    setSelectedCar,
    setCarPlans,
    setDeliverLng,
    setDeliverLat,
    selectedCompany,
    getBranches,
    setSelectedBranch,
    selectedBranch,
    allBranches,
    setSelectedCompany,
    allCompanies,
    branchExtraServicesIds,
    setBranchExtraServicesIds,
    allyExtraServicesIds,
    setAllyExtraServicesIds,
    setExtraServices,
    branches,
    selectedCar,
    requestCarsvariables,
    getCars,
    mapChange,
    deliverLat,
    handoverprice,
    distanceCarUser,
    deliverLng,
    setIsPickSameReturn,
    setIsDelivery,
    recalculateRentPrice,
    carId: selectedCar?.id,
    isRentalInstallment,
    setwithInstallment,
    withInstallment,
    BookingPriceRes,
  });

  function a11yProps(index) {
    return {
      id: `wrapped-tab-${index}`,
      "aria-controls": `wrapped-tabpanel-${index}`,
    };
  }
  const [value, setValue] = React.useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  function getDropoffBranches() {
    if (selectedCar && dropOffCity) {
      const availableHandoverBranches = selectedCar?.branch?.availableHandoverBranches;
      const filteredBranches = availableHandoverBranches
        ?.filter((b) => b.areaId == dropOffCity?.id)
        .map((i) => ({
          label: i.name,
          value: i.id,
          ...i,
        }));
      return filteredBranches;
    }
  }
  const getRentalAudits = () => {
    setOpenTimeLineModal(true);
  };
  //Form Submit
  useEffect(() => {
    if (!BookingPriceRes?.aboutRentPrice?.couponErrorMessage) {
      setCouponAvailability(null);
    } else {
      setCouponAvailability("Invalid coupon");
    }
  }, [BookingPriceRes]);
  async function handleSubmitRent(e, params) {
    if (e) e.preventDefault();
    setClicked(true);
    const variables = {
      carId: selectedCar?.value,
      paymentMethod,
      pickUpCityId: pickUpCity?.id,
      pickUpDate: pickUpDate.locale("en").format("DD/MM/YYYY"), // convertLocalTimeToUtc(pickUpDate), // moment(pickUpDate?._id || pickUpDate).format("DD/MM/YYYY"),
      pickUpTime: pickUpDate.locale("en").format("HH:mm:ss"), // convertLocalTimeToUtc(pickUpDate), // `${moment(pickUpDate?._id || pickUpDate).format("HH:mm")}:00`,
      dropOffBranchId: handoverTobranch ? selectedDropoffBranch?.value : selectedBranch?.value ,
      dropOffCityId: !handoverTobranch ? pickUpCity?.id : +dropOffCity?.id,
      dropOffDate:
        bookingType == "rent-to-own" ? undefined : dropOffDate.locale("en").format("DD/MM/YYYY"), // convertLocalTimeToUtc(dropOffDate), // moment(dropOffDate).format("DD/MM/YYYY"),
      dropOffTime:
        bookingType == "rent-to-own" ? undefined : dropOffDate.locale("en").format("HH:mm:ss"), // convertLocalTimeToUtc(dropOffDate),
      insuranceId,
      userId: customerDetails?.users?.collection?.[0]?.id,
      deliverLat: isDelivery ? deliverLat || rentalDetails?.rentalDetails?.deliverLat : null,
      deliverLng: isDelivery ? deliverLng || rentalDetails?.rentalDetails?.deliverLng : null,
      deliveryPrice: isDelivery ? deliveryPrice : undefined,
      handoverPrice:
        !handoverTobranch && !isDelivery // same as pickup and daily or monthly => do not send handover value
          ? undefined
          : handoverTobranch || (handoverToSamePickup && isDelivery) // sends handover value from ally company page in all booking types if different pickup location or send handover value from branch page in delivery tab and same as pickup location
          ? handoverprice
          : undefined,
      deliverType:
        isDelivery && handoverToSamePickup
          ? "two_ways"
          : isDelivery && (handoverTobranch || !handoverToSamePickup )
          ? "one_way"
          : "no_delivery",
      deliverAddress: isDelivery
        ? deliverAddress || rentalDetails?.rentalDetails?.deliverAddress || pickUpCity?.id
        : "",
      allyExtraServices: [...new Set(allyExtraServicesIds)],
      branchExtraServices: [...new Set(branchExtraServicesIds.map((b) => +b))],
      notes: Noteref.current,
      isUnlimited: unLimited,
      ownCarPlanId: bookingType == "rent-to-own" ? (plan?.id ? plan?.id : undefined) : undefined,
      handoverAddress: isDelivery || handoverTobranch
        ? deliverAddress || rentalDetails?.rentalDetails?.deliverAddress || pickUpCity?.id
        : "",
      handoverLat: handoverTobranch
        ? handoverlat || rentalDetails?.rentalDetails?.handoverLat
        : handoverToSamePickup
        ? deliverLat || rentalDetails?.rentalDetails?.deliverLat
        : undefined,
      withInstallment: !bookingId && bookingType != "rent-to-own" ? withInstallment : undefined,

      handoverLng: handoverTobranch
        ? handovderLng || rentalDetails?.rentalDetails?.handoverLng
        : handoverToSamePickup
        ? deliverLng || rentalDetails?.rentalDetails?.deliverLng
        : undefined,
    };

    if (suggestedPricePerDayRef?.current?.length) {
      variables.suggestedPrice = +suggestedPricePerDayRef.current;
    } else {
      variables.suggestedPrice = 0;
    }

    if (bookingId) {
      if (
        rentalDetails?.rentalDetails?.allyRentalRejections?.find(
          (reason) => reason.branchId == selectedCar?.branch?.id,
        )
      ) {
        swal({
          title: formatMessage({
            id: "You've chosen an ally who has previously declined this booking",
          }),
          text: formatMessage({ id: "Would you like to proceed?" }),
          icon: "warning",
          buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
          dangerMode: true,
        }).then((willDelete) => {
          if (willDelete) {
            setDimmed(true);
            variables.rentalId = +bookingId;
            if (BookingPriceRes?.aboutRentPrice?.couponErrorMessage) {
              swal({
                title: "",
                text: BookingPriceRes?.aboutRentPrice?.couponErrorMessage,
                icon: "warning",
                buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
                dangerMode: true,
              }).then((willDelete) => {
                if (willDelete) {
                  variables.couponId = undefined;
                  EditBookingMutation({
                    variables,
                  })
                    .then(() => {
                      NotificationManager.success(formatMessage({ id: "success.edit.rental" }));
                      recalculateRentPrice();
                      refetchBooking().then(() => {
                        setTimeout(() => {
                          setDimmed(false);
                        }, 1500);
                      });
                    })
                    .catch((err) => {
                      setDimmed(false);
                      NotificationManager.error(err.message);
                    });
                } else {
                  return;
                }
              });
            } else {
              EditBookingMutation({
                variables,
              })
                .then(() => {
                  NotificationManager.success(formatMessage({ id: "success.edit.rental" }));
                  recalculateRentPrice();
                  refetchBooking().then(() => {
                    setTimeout(() => {
                      setDimmed(false);
                    }, 1500);
                  });
                })
                .catch((err) => {
                  setDimmed(false);
                  NotificationManager.error(err.message);
                });
            }
          }
        });
      } else {
        if (BookingPriceRes?.aboutRentPrice?.couponErrorMessage) {
          swal({
            title: "",
            text: BookingPriceRes?.aboutRentPrice?.couponErrorMessage,
            icon: "warning",
            buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
            dangerMode: true,
          }).then((willDelete) => {
            if (willDelete) {
              variables.couponId = undefined;
              setDimmed(true);
              variables.rentalId = +bookingId;
              EditBookingMutation({
                variables,
              })
                .then(() => {
                  NotificationManager.success(formatMessage({ id: "success.edit.rental" }));
                  recalculateRentPrice();
                  refetchBooking().then(() => {
                    setTimeout(() => {
                      setDimmed(false);
                    }, 1500);
                  });
                })
                .catch((err) => {
                  setDimmed(false);
                  NotificationManager.error(err.message);
                });
            } else {
              return;
            }
          });
        } else {
          setDimmed(true);
          variables.rentalId = +bookingId;
          variables.couponId = +copounId;

          EditBookingMutation({
            variables,
          })
            .then(() => {
              NotificationManager.success(formatMessage({ id: "success.edit.rental" }));
              recalculateRentPrice();
              refetchBooking().then(() => {
                setTimeout(() => {
                  setDimmed(false);
                }, 1500);
              });
            })
            .catch((err) => {
              setDimmed(false);
              NotificationManager.error(err.message);
            });
        }
      }
    } else {
      if (!dropOffCity && !isPickSameReturn) {
        return;
      }
      setDimmed(true);
      variables.loyaltyType = FursanVerified && FursanChecked ? "alfursan" : undefined;
      // return
      if (BookingPriceRes?.aboutRentPrice?.couponErrorMessage) {
        swal({
          title: "",
          text: BookingPriceRes?.aboutRentPrice?.couponErrorMessage,
          icon: "warning",
          buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
          dangerMode: true,
        }).then((willDelete) => {
          if (willDelete) {
            variables.couponId = undefined;
            setDimmed(true);
            createBookingMutation({
              variables,
            })
              .then(() => {
                NotificationManager.success(formatMessage({ id: "success.create.rental" }));
                refetchAllBookings().finally(() => {
                  setTimeout(() => {
                    setDimmed(false);
                    history.push("/cw/dashboard/bookings");
                  }, 1500);
                });
              })
              .catch((err) => {
                setDimmed(false);
                NotificationManager.error(err.message);
              });
          } else {
            setDimmed(false);

            return;
          }
        });
      } else {
        setDimmed(true);

        variables.couponId = +copounId;

        createBookingMutation({
          variables,
        })
          .then(() => {
            NotificationManager.success(formatMessage({ id: "success.create.rental" }));
            refetchAllBookings().finally(() => {
              setTimeout(() => {
                setDimmed(false);
                history.push("/cw/dashboard/bookings");
              }, 1500);
            });
          })
          .catch((err) => {
            setDimmed(false);
            NotificationManager.error(err.message);
          });
      }
    }
  }
  const OpenAlert = (val) => {
    swal({
      title: formatMessage({
        id: "Editing this booking period will close the pending extensions",
      }),
      icon: "warning",
      buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
      dangerMode: true,
    }).then((willDelete) => {
      if (willDelete) {
        rejectRequestHandler(rentalDetails?.rentalDetails?.pendingExtensionRequest?.id, val);
      }
    });
  };
  function rejectRequestHandler(id, val) {
    rejectRentalDateExtensionRequest({ variables: { rentalExtensionId: id } })
      .then((res) => {
        if (res.data.rejectRentalDateExtensionRequest.status === "success") {
          clearBranchSelection();
          setPickUpCity(null);
          setTimeout(() => {
            refetchBooking();

            setPickUpDate(val);
            if (bookingType == "rent-to-own") {
              setDropOffDate(moment(val).locale("en").add(plan.noOfMonths, "months"));
            } else {
              setDropOffDate(val);
            }
          }, 100);
          NotificationManager.success(<FormattedMessage id="Done successfully" />);
        } else {
          NotificationManager.error(<FormattedMessage id=" Error " />);
        }
      })
      .catch((err) => NotificationManager.error(err.message));
  }
  const shareLocation = () => {
    const url = bookingId
      ? `https://www.google.com/maps?q=${rentalDetails?.rentalDetails.deliverLat},${rentalDetails?.rentalDetails?.deliverLng}`
      : `https://www.google.com/maps?q=${deliverLat},${deliverLng}`;
    navigator.clipboard.writeText(url).then(() => {
      swal({
        title: formatMessage({ id: "Link Copied" }),

        icon: "success",
      });
    });
  };
  //JSX
  return (
    <>
      {(editingRental || creatingRental) && <FullPageLogoLoader />}
      <Header
        {...{
          formatMessage,
          bookingId,
          location,
          rentalDetails,
          resendRental,
          NotificationManager,
          refetchBooking,
          messages,
          userInfo,
          getRentalAudits,
          setIsExtensionModalOpen,
        }}
      />
      {!bookingId && (
        <div className={`mt-4 ${styles.customerDetails}`}>
          <h3>
            <FormattedMessage id="rental.enterphone" />
          </h3>
          <GettingCustomerDetails
            setCustomerDetails={setCustomerDetails}
            setCustomerId={setCustomerId}
            setFusranChecked={setFusranChecked}
            setFursanVerified={setFursanVerified}
          />
        </div>
      )}
      <Tabs value={value} onChange={handleChange}>
        <div>
          <AppBar position="static" className="mt-2 tabview">
            <TabList>
              <Tab>
                <FormattedMessage id="basicinformation" />
              </Tab>
              {rentalDetails?.rentalDetails?.installments?.length ? (
                <Tab>
                  <FormattedMessage id="Rental Installments" />
                </Tab>
              ) : null}
            </TabList>
          </AppBar>

          <TabPanel>
            {(customerDetails || customerDetailsRes) && (
              <div className="row p-1" style={{ marginTop: "0px" }}>
                <div
                  className="d-flex flex-column col-md-7"
                  style={{ display: "grid !important", gridRowGap: "10px" }}
                >
                  <div
                    className="mt-3 mb-1"
                    style={{ gap: "20px" }}
                    onChange={(e) => {
                      const { value } = e.target || {};
                      setChanged(true);
                      setBookingType(value);

                      switch (value) {
                        case "daily":
                          setMonths(0);
                          break;
                        case "monthly":
                          setMonths(3);

                          setDropOffDate(
                            moment(pickUpDate)
                              .locale("en")
                              .add(30 * 3, "days"),
                          );

                          break;
                        default:
                          null;
                      }
                      clearBranchSelection();

                      setSelectedCompany(null);
                      setBranchExtraServicesIds([]);
                      setSelectedCar(null);
                    }}
                  >
                    <h4>
                      <FormattedMessage id="rental.bookingType" />
                    </h4>
                    <div className="d-flex" style={{ gap: "0.1rem", flexWrap: "wrap" }}>
                      {bookingsTypes.map((type) => (
                        <div className="form-check form-check-inline" key={type.id}>
                          <input
                            className="form-check-input"
                            type="radio"
                            checked={type.name === bookingType}
                            name="bookingType"
                            id={type.name}
                            disabled={
                              (bookingId && bookingType == "rent-to-own") ||
                              (bookingId && type?.name == "rent-to-own")
                            }
                            value={type.name}
                            onChange={() => {
                              setChanged(true);
                            }}
                          />
                          <label className="form-check-label p-0 m-2" htmlFor={type.name}>
                            <FormattedMessage id={type?.name} />
                          </label>
                        </div>
                      ))}
                    </div>
                   
                  </div>
                <DeliveryComponent 
                isDelivery={isDelivery}
                setIsDelivery={setIsDelivery}
                setSelectedCompany={setSelectedCompany}
                setHandoverToBranch={setHandoverToBranch}
                handoverTobranch={handoverTobranch}
                setHandoverToSamePickup={setHandoverToSamePickup}
                handoverToSamePickup={handoverToSamePickup}
                clearBranchSelection={clearBranchSelection}
                setSelectedCar={setSelectedCar}
                setSelectedDropoffBranch={setSelectedDropoffBranch}

                />
                  {!bookingId && bookingType != "rent-to-own" ? (
                    <div style={{ marginBottom: "15px" }}>
                      <input
                        key={`${isRentalInstallment}`}
                        id="isInstallment"
                        style={{ cursor: "pointer" }}
                        type="checkbox"
                        defaultChecked={isRentalInstallment}
                        checked={withInstallment}
                        onChange={(e) => {
                          const { checked } = e.target || {};
                          setChanged(true);
                          setwithInstallment(checked);
                          if (checked) {
                            setDropOffDate(
                              moment(pickUpDate)
                                .locale("en")
                                .add(30 * 3, "days"),
                            );
                          }
                        }}
                        disabled={isPaidInstallment}
                      />
                      <label className="form-check-label p-0 m-2" htmlFor="isInstallment">
                        <FormattedMessage id="Installments.booking" />
                      </label>
                    </div>
                  ) : null}

                  <div>
                    <h4>
                      <FormattedMessage id="rental.bookingTiming" />
                    </h4>

                    {bookingId && gettingAreas && (
                      <>
                        <CircularProgress />
                      </>
                    )}
                    {(!bookingId || !(bookingId && !editDatedReady)) && (
                      <div className="d-flex justify-content-between flex-wrap">
                        <DateTimePickerCustom
                          autoOk
                          label={formatMessage({ id: "rental.pickupDateTime" })}
                          maxDate={
                            bookingType == "rent-to-own"
                              ? moment(new Date()).locale("en").add(20, "days")
                              : moment(new Date()).locale("en").add(20, "year")
                          }
                          value={pickUpDate}
                          onChange={(val) => {
                            setChanged(true);
                            if (rentalDetails?.rentalDetails?.hasPendingExtensionRequests) {
                              OpenAlert(val);
                              return;
                            }
                            clearBranchSelection();
                            setPickUpCity(null);
                            setSelectedCompany(null)
                            setSelectedCar(null)
                            setTimeout(() => {
                              setPickUpDate(val);
                              if (bookingType == "rent-to-own") {
                                setDropOffDate(
                                  moment(val).locale("en").add(plan.noOfMonths, "months"),
                                );
                              }
                            }, 100);
                          }}
                          minDateMessage={formatMessage({ id: "pleaseSelectSuitablePicupDate" })}
                        />
                        {bookingType !== "monthly" && (
                          <DateTimePickerCustom
                            autoOk
                            disabled={bookingType == "rent-to-own"}
                            value={dropOffDate}
                            label={formatMessage({ id: "rental.dropoffDateTime" })}
                            onChange={(val) => {
                              if (rentalDetails?.rentalDetails?.hasPendingExtensionRequests) {
                                OpenAlert(val);
                                return;
                              }
                              setChanged(true);
                              setTimeout(() => {
                                setDropOffDate(val);
                              }, 100);
                            }}
                            minDate={pickUpDate}
                            minDateMessage={formatMessage({
                              id: "validation.fromMustBeLessThanTo",
                            })}
                          />
                        )}
                      </div>
                    )}
                    {bookingType === "monthly" && (
                      <div className="row">
                        <div className="col-md-6">
                          <FormControl variant="standard">
                            <InputLabel id="demo-simple-select-outlined-label">
                              <FormattedMessage id="months.count" />
                            </InputLabel>
                            <Select
                              key={months}
                              placeholder={formatMessage({ id: "months.count" })}
                              options={MonthsOfRent}
                              value={MonthsOfRent.find((month) => +month.value == +months)}
                              onChange={(selection) => {
                                setMonths(+selection.value);
                                setDropOffDate(() =>
                                  moment(pickUpDate)
                                    .locale("en")
                                    .add(+selection.value * 30, "days"),
                                );
                                setChanged(true);
                              }}
                              getOptionLabel={(options) => <FormattedMessage id={options.label} />}
                            ></Select>
                          </FormControl>
                        </div>
                        <div className="col-md-6">
                          <TextField
                            key={`${dropOffDate}-${monthTime}`}
                            id="time"
                            label={formatMessage({ id: "rental.dropoffTime" })}
                            type="time"
                            defaultValue={
                              monthTime && monthTime !== "Invalid date"
                                ? monthTime
                                : moment(dropOffDate).locale("en").format("HH:mm")
                            }
                            InputLabelProps={{ shrink: true }}
                            onChange={(e) => {
                              setMonthTime(e.target.value);
                            }}
                            inputProps={{ step: 300 * 6 }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                  <div>
                    <h4>
                      <FormattedMessage id="rental.bookingLocation" />
                    </h4>
                    {isDelivery && (
                      <div
                        style={{
                          minHeight: "300px",
                          width: "100%",
                          position: "relative",
                          marginBottom: "30px",
                        }}
                      >
                       <Map
                          latitude={deliverLat}
                          longitude={deliverLng}
                          setLatitude={(lat) => setDeliverLat(lat)}
                          setLongitude={(lng) => setDeliverLng(lng)}
                          centerlat={deliverLat}
                          centerlng={deliverLng}
                          setMapChange={setMapChange}
                          mapChange={mapChange}
                          isBooking
                          setDeliverAddress={setDeliverAddress}
                        />
                      </div>
                    )}
                    <div
                      className="form-check mt-3 d-flex align-items-center justify-content-between"
                      style={{ padding: "0" }}
                    >
                   <CitiesDropDown 
                   className="w-100"
                   valueAttribute="id"
                   inBooking={false}
                     setSelectedCity={(id)=>{
                      setDeliverLat(id?.centerLat);
                      setDeliverLng(id?.centerLng);
                      setMapChange(!mapChange);

                      setPickUpCity(id)
                     }}
                     selectedCity={+pickUpCity?.id}
                     multiple={false}
                   /> 

                    </div>
                    {(handoverTobranch  && !bookingId ) ||
                    (bookingId  && dropOffCity && handoverTobranch ) ||
                    (bookingId  && isDelivery && handoverTobranch ) ? (
                      <Autocomplete
                        style={{ marginTop: "25px" }}
                        id="dropoff-location"
                        className="mb-2"
                        isRequired={handoverTobranch}
                        options={
                          AreasRes?.areas?.filter((city) => +city?.id !== pickUpCity?.id) || []
                        }
                        getOptionLabel={(option) => option?.[`${locale}Name`]}
                        value={dropOffCity}
                        disableClearable
                        onChange={(e, val) => {
                          setHandoverLat(val.centerLat);
                          setHandoverLng(val.centerLng);
                          setDropOffCity(val);
                        }}
                        loading={gettingAreas}
                        disabled={!customerId}
                        renderInput={(params) => (
                          <TextField
                            error={clicked && Boolean(!dropOffCity)}
                            {...params}
                            label={<FormattedMessage id="dropoff-location" />}
                            variant="outlined"
                            fullWidth
                            helperText={
                              !dropOffCity && !isPickSameReturn && clicked ? (
                                <FormattedMessage id="thisfieldisrequired" />
                              ) : null
                            }
                          />
                        )}
                      />
                    ) : null}
                    {!ally_id ? (
                            <>
                              <h6 className="mt-1 mb-3">
                                {formatMessage({ id: "selecting.company" })}
                              </h6>
                              <Select
                                key={`${pickUpCity} ${dropOffCity}`}
                                options={allCompanies}
                                value={allCompanies?.find(
                                  (val) => val.value == selectedCompany?.value,
                                )|| []}
                                placeholder={formatMessage({ id: "selecting.company" })}
                                onChange={(selection) => {
                                  clearBranchSelection();
                                  setCopounId(null);
                                  setSelectedCompany(selection);
                                  setBranchExtraServicesIds([]);
                                  setCopounId(null);
                                  setCouponCode(null);
                                  setSelectedCar(null);
                                }}
                                isLoading={!!loadingAllyCompanies}
                                className="dropdown-select"
                                styles={customStyles}
                              />
                            </>
                          ) : null}
                  </div>
                  {
                    selectedCompany?.value &&
                    <BranchesDropdownForBooking 
                    allyId={selectedCompany?.value}
                    canDelivery={ bookingType === "delivery" ? true : undefined}
                    isRentToOwn={ bookingType == "rent-to-own" ? true : undefined}
                    areaIds={pickUpCity?.id ? [pickUpCity?.id] : rentalDetails?.rentalDetails?.pickUpCityId ? [rentalDetails?.rentalDetails?.pickUpCityId] : []}
                      setSelectedBranch={(selection)=>{
                      setSelectedBranch(selection);
                    }}
                    valueAttribute="id"
                    selectedBranch={selectedBranch}
                    /> 
                  }
          {
            selectedCompany?.value &&
            <CarsDropDownForBooking
            selectedBranch={selectedBranch}
            canDelivery={ bookingType === "delivery" ? true : undefined}
            isRentToOwn={ bookingType == "rent-to-own" ? true : undefined}
            requestCarsvariables={requestCarsvariables}
            setSelectedCar={(sel)=>{
              setSelectedCar(sel)
            }}
            selectedCar={selectedCar}
          />
          }
           {(!bookingId &&
                          selectedCar &&
                          handoverTobranch   &&
                         selectedCompany?.value &&
                          bookingType != "rent-to-own") ||
                        (bookingId &&
                          selectedCar &&
                         handoverTobranch &&
                         selectedCompany?.value
                         &&
                          bookingType != "rent-to-own") ? (
                          <div className="mt-3 mb-3">
                            <>
                              <h6 className="mt-1 mb-3">
                                {formatMessage({
                                  id: "select.Dropoff branch",
                                })}
                              </h6>
                              <Select
                                key={`${selectedCompany} ${selectedBranch} ${selectedCar} ${selectedDropoffBranch}`}
                                options={getDropoffBranches()}
                                defaultValue={getDropoffBranches()?.find(
                                  (val) =>
                                    +val.value === +selectedDropoffBranch?.value ||
                                    +val.value === +selectedDropoffBranch ||
                                    +val.value === +rentalDetails?.rentalDetails?.dropOffBranchId,
                                )}
                                placeholder={formatMessage({
                                  id: "select.Dropoff branch",
                                })}
                                onChange={(selection) => {
                                  setChanged(true);
                                  setSelectedDropoffBranch(selection);
                                  setCopounId(null);
                                  setCouponCode(null);
                                }}
                                className="dropdown-select"
                                styles={customStyles}
                              />
                            </>
                          </div>
                        ) : null}
                          {selectedCompany && selectedBranch && selectedCar && isDelivery && (
                          <div className="mt-2">
                            <CustomTextField
                              fullWidth
                              name="dsitance"
                              value={distanceCarUser}
                              disabled
                            />
                            <CustomTextField
                              key={deliveryPrice}
                              fullWidth
                              // disabled
                              className="mt-2"
                              name="delivery_price"
                              onInput={(e) => {
                                if (e.target.value.includes(".")) {
                                  e.target.value = e.target.value.toString().slice(0, 9);
                                } else {
                                  e.target.value = e.target.value.toString().slice(0, 6);
                                }
                              }}
                              defaultValue={deliveryPrice}
                              onBlur={(e) =>
                              {
                                setDeliveryPrice(+e.target.value)
                              }
                              }
                            />
                          </div>
                        )}

               {handoverTobranch &&
                        selectedCompany &&
                        selectedBranch &&
                        selectedCar &&
                       
                        bookingType != "rent-to-own" ? (
                          <>
                            <h6 className="mt-1 mb-3">
                              {formatMessage({
                                id: "handover_branch_price",
                              })}
                            </h6>
                            <CustomTextField
                              fullWidth
                              placeholder={formatMessage({
                                id: "handover_branch_price",
                              })}
                              noLabel
                              name="handover_branch_price"
                              value={handoverprice}
                              // disabled
                              onInput={(e) => {
                                if (e.target.value.includes(".")) {
                                  e.target.value = e.target.value.toString().slice(0, 9);
                                } else {
                                  e.target.value = e.target.value.toString().slice(0, 6);
                                }
                              }}
                              onChange={(e) => {
                                setHandOverPrice(+e.target.value);
                                // setChanged(true);
                              }}
                            />
                          </>
                        ) : null}
                            {handoverToSamePickup &&
                        selectedCompany &&
                        selectedBranch &&
                        selectedCar &&
                        isDelivery &&
                        bookingType != "rent-to-own" ? (
                          <>
                            <h6 className="mt-1 mb-3">
                              {formatMessage({
                                id: handoverToSamePickup ?  "return_to_same_Pickup_location" :   "handover_fees",
                              })}
                            </h6>
                            <CustomTextField
                              fullWidth
                              placeholder={formatMessage({
                                id: "handover_fees",
                              })}
                              noLabel
                              name="handover_fees"
                              value={handoverprice}
                              // disabled
                              onInput={(e) => {
                                if (e.target.value.includes(".")) {
                                  e.target.value = e.target.value.toString().slice(0, 9);
                                } else {
                                  e.target.value = e.target.value.toString().slice(0, 6);
                                }
                              }}
                              onChange={(e) => {
                                setHandOverPrice(+e.target.value);
                                // setChanged(true);
                              }}
                            />
                          </>
                        ) : null}
                            {
                               selectedBranch && selectedCar &&  selectedCompany?.value ? 
                             <ExtraServiceComponent 
                             selectedBranch={selectedBranch}
                             selectedCar={selectedCar}
                             setChanged={setChanged}
                             branchExtraServicesIds={branchExtraServicesIds}
                             setBranchExtraServicesIds={setBranchExtraServicesIds}
                             rentalDetails={rentalDetails}
                             setAllyExtraServicesIds={setAllyExtraServicesIds}
                             bookingId={bookingId}
                             setunLimited={setunLimited}
                             allyExtraServicesIds ={allyExtraServicesIds}
                             locale={locale}
                             unLimited={unLimited}
                             />
                             : null
                            }
                            {
                               selectedBranch && selectedCar && selectedCompany?.value ? 
                               <CouponDiscountComponent 
                               selectedBranch={selectedBranch}
                               selectedCompany ={selectedCompany}
                               getCarCouponAvailable={getCarCouponAvailable}
                               customerDetails={customerDetails || customerDetailsRes}
                               copounref={copounref} 
                               copounCode={copounCode} 
                               setCouponCode={setCouponCode}
                               selectedCar={selectedCar}
                               setCopounId={setCopounId} 
                               setChanged={setChanged}
                               rentalDetails={rentalDetails}
                               setAllyExtraServicesIds={setAllyExtraServicesIds}
                               bookingId={bookingId}
                               setunLimited={setunLimited}
                               allyExtraServicesIds ={allyExtraServicesIds}
                               locale={locale}
                               unLimited={unLimited}
                              
                              />
                              : null
                            }
                            {
                              selectedBranch && selectedCar && selectedCompany?.value ?
                              <InsuranceDropDown 
                              selectedCar={selectedCar}
                              setChanged={setChanged}
                              setInsuranceId={setInsuranceId}
                              insuranceId={insuranceId}
                              />
                              : null
                            }
                              {selectedCompany && selectedBranch && selectedCar && insuranceId && selectedCompany?.value  ? (
                                <>
                          <div className="alert alert-info mt-2 mb-2" role="alert">
                            <p>{selectedCar.label}</p>
                            <BookingPriceSummary2
                              handoverChecked={handoverChecked || handoverTobranch}
                              BookingPrice={selectedCar ? BookingPriceRes : {}}
                              handoverToSamePickup={handoverToSamePickup}
                              calculatingPrice={calculatingPrice}
                              insurance={insuranceId}
                              bookingId={bookingId}
                              BookingDetails={rentalDetails}
                              isUnlimited={unLimited}
                              bookingType={bookingType}
                              change={changed}
                              plan={plan}
                            />
                          </div>
                            {selectedCar &&
                        ((insuranceId && bookingType != "rent-to-own") ||
                          (!insuranceId && bookingType == "rent-to-own") ||
                          (insuranceId && bookingType == "rent-to-own")) && (
                          <>
                            <FormControlLabelContainer labelId="paymetMethod">
                              <RadioGroupContainer value={paymentMethod}>
                                {["cash", "online"].map((type) => (
                                  <FormControlLabel
                                    key={type}
                                    disabled={(!selectedCar?.id && !selectedCar?.value ) || (type == "online" && bookingId &&  rentalDetails?.rentalDetails?.isPaid) || rentalDetails?.rentalDetails?.isPaid ||  rentalDetails?.rentalDetails?.pendingPaymentOrder}
                                    value={type.toUpperCase()}
                                    control={<Radio color="primary" />}
                                    checked={paymentMethod?.toLocaleLowerCase() == type}
                                    label={formatMessage({ id: type.toUpperCase() })}
                                    className="m-0"
                                    onChange={(e) => {
                                      setChanged(true);
                                      if (e.target.value == "ONLINE") {
                                        setPaymentMethod("ONLINE");
                                        return;
                                      }
                                      setPaymentMethod("CASH");
                                    }}
                                  />
                                ))}
                              </RadioGroupContainer>
                            </FormControlLabelContainer>
                            {paymentMethod != "CASH" ? (
                              <div>
                                <h4 style={{ fontWeight: "bold" }} className="mt-4 mb-4">
                                  <Checkbox
                                    disabled={bookingId}
                                    checked={FursanChecked}
                                    color="primary"
                                    onChange={() => {
                                      setFusranChecked(!FursanChecked);
                                      setIsOpen(!FursanChecked);
                                    }}
                                    inputProps={{ "aria-label": "secondary checkbox" }}
                                  />
                                  <FormattedMessage id="Alfursan" />
                                </h4>
                              </div>
                            ) : null}
                            <CustomTextField
                              id="suggestedPricePerDay"
                              fullWidth
                              name="suggestedPricePerDay"
                              defaultValue={suggestedPricePerDayRef.current}
                              onChange={(e) => {
                                const price = e.target.value;
                                if (/^[0-9]+(\.)?[0-9]*$/.test(price.toString()) || price === "") {
                                  suggestedPricePerDayRef.current = e.target.value;
                                }
                              }}
                            />
                            {bookingId && (
                              <TextField
                                id="note"
                                label={<FormattedMessage id="note" />}
                                multiline
                                style={{ marginBottom: "-50px" }}
                                rows={4}
                                defaultValue={Noteref.current}
                                variant="outlined"
                                // onFocus={(e) => {
                                //   setChanged(true);
                                //   window.setTimeout(() => {
                                //     document.querySelector("#note").focus();
                                //   }, 1000);
                                // }}

                                onChange={(e) => {
                                  Noteref.current = e.target.value;
                                }}
                              />
                            )}
                          </>
                        )}
                                </>
                          
                        ) : null}
                       
                  <div style={{ marginTop: "80px" }}>
                    <button
                      type="submit"
                      style={{ width: "100%" }}
                      className="btn btn-primary text-white btn-icon mt-2 mb-2"
                      onClick={(e) => handleSubmitRent(e)}
                      disabled={!(selectedCompany && selectedBranch && selectedCar) || (!insuranceId &&  bookingType != "rent-to-own" )}
                    >
                      <FormattedMessage id={bookingId ? "button.save" : "Rent"} />
                    </button>
                  </div>
                </div>
                <div className="col-md-5" style={{ height: "fit-content" }}>
                  <CustomerDataDisplay
                    customerDetailsRes={customerDetails || customerDetailsRes}
                    walletBalance={walletBalance}
                  />
                </div>
              </div>
            )}
          </TabPanel>
          <TabPanel style={{ height: "100%" }}>
            <div className="col-md-12" style={{ height: "100%" }}>
              <InstallmentsTable
                rentalId={rentalDetails?.rentalDetails?.id}
                Installments={rentalDetails?.rentalDetails?.mergedInstallments}
                refetchBooking={refetchBooking}
                bookDetails={rentalDetails?.rentalDetails}
              />
            </div>
          </TabPanel>
        </div>
      </Tabs>

      <Modals
        {...{
          extensionModalOpen,
          setIsExtensionModalOpen,
          rentalDetails,
          refetchBooking,
          opneTimeLineModal,
          setOpenTimeLineModal,
          bookingId,
          OpenRejectionModal,
          setOpenRejectionModal,
        }}
        rentalDetails={rentalDetails}
        company={allCompanies?.find((val) => val.value == selectedCompany?.value)}
      />
      <FursanVerification
        isOpen={isopen}
        setIsOpen={setIsOpen}
        setFusranChecked={setFusranChecked}
        customerDetailsRes={customerDetails || customerDetailsRes}
        setFursanVerified={setFursanVerified}
        FursanVerified={FursanVerified}
      />
    </>
  );

  function carName(c) {
    return `${c.transmissionName} / ${c.make?.[`${locale}Name`]} - ${
      c?.carModel?.[`${locale}Name`]
    } - ${c?.carVersion?.[`${locale}Name`] ? c?.carVersion?.[`${locale}Name`] : ""} - ${
      c.year
    } | [${formatMessage({ id: "branchName" })}: ${c.branch[`${locale}Name`]}]
    [${formatMessage({ id: "daily" })}: ${c?.dailyPrice} ${formatMessage({
      id: "rental.weeklyPrice",
    })}: ${c?.weeklyPrice ? c?.weeklyPrice : ""} ${formatMessage({
      id: "rental.monthlyPrice",
    })}: ${c?.monthlyPrice}]`;
  }
}

export default AddEditBooking2;
