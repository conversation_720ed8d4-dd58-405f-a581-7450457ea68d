import React, { useEffect, useState } from "react";
import { useLocation, useHistory } from "react-router-dom";
import { useIntl } from "react-intl";
import { useLazyQuery } from "@apollo/client";
import { NotificationManager } from "react-notifications";
import { Helmet } from "react-helmet";
import PageTitleBar from "components/PageTitleBar/PageTitleBar";
import IntlMessages from "util/IntlMessages";
import PaymentStatusModal from "../PaymentFlow/PaymentStatusModal";
import {
  PAYMENT_STATUS_QUERY,
  INSTALLMENT_PAYMENT_STATUS_QUERY,
  EXTENSION_PAYMENT_STATUS_QUERY,
} from "../PaymentFlow/graphql";
import "./TamaraResult.scss";

const TamaraResult = () => {
  const { formatMessage } = useIntl();
  const location = useLocation();
  const history = useHistory();
  const [paymentStatus, setPaymentStatus] = useState("checking");
  const [error, setError] = useState(null);

  // Extract URL parameters
  const urlParams = new URLSearchParams(location.search);
  const rentalId = urlParams.get("rentalId");
  const paymentStatus_param = urlParams.get("paymentStatus");
  const orderId = urlParams.get("orderId");
  const declineType = urlParams.get("decline_type");

  // Get stored payment context from sessionStorage
  const paymentContext = JSON.parse(sessionStorage.getItem("paymentContext") || "{}");
  const { extensionId, isInstallment } = paymentContext;

  // Debug logging
  console.log("TamaraResult Debug:", {
    rentalId,
    paymentStatus_param,
    orderId,
    declineType,
    paymentContext,
    extensionId,
    isInstallment,
  });

  // Choose the appropriate query based on payment type
  const statusQuery = extensionId
    ? EXTENSION_PAYMENT_STATUS_QUERY
    : isInstallment
    ? INSTALLMENT_PAYMENT_STATUS_QUERY
    : PAYMENT_STATUS_QUERY;

  const [checkPaymentStatus, { loading }] = useLazyQuery(statusQuery, {
    errorPolicy: "all",
  });

  useEffect(() => {
    const checkStatus = async () => {
      // Use rental ID from URL or sessionStorage
      const currentRentalId = rentalId || paymentContext.rentalId;

      if (!currentRentalId && !extensionId) {
        setError(
          formatMessage({
            id: "Payment session expired. Please check your payment status in the dashboard.",
          }),
        );
        setPaymentStatus("failed");
        return;
      }

      // Always check with backend to get the actual payment status
      // Don't rely on URL parameters as they might not reflect the real status
      // Use orderId as checkoutId for Tamara payments
      try {
        const variables = extensionId
          ? { checkoutId: orderId, rentalExtensionId: extensionId }
          : { checkoutId: orderId, rentalId: currentRentalId };

        const result = await checkPaymentStatus({ variables });

        let status;
        let errors;

        if (extensionId) {
          status = result.data?.extensionGetPaymentStatus?.status;
          errors = result.data?.extensionGetPaymentStatus?.errors;
        } else if (isInstallment) {
          status = result.data?.installmentGetPaymentStatus?.status;
          errors = result.data?.installmentGetPaymentStatus?.errors;
        } else {
          status = result.data?.getPaymentStatus?.status;
          errors = result.data?.getPaymentStatus?.errors;
        }

        setPaymentStatus(status || "failed");

        if (status === "paid") {
          sessionStorage.removeItem("paymentContext");
          // Redirect to booking page after 3 seconds
          const bookingId = rentalId || paymentContext.rentalId;
          setTimeout(() => {
            if (bookingId) {
              history.push(`/cw/dashboard/bookings/${bookingId}`);
            } else {
              history.push("/cw");
            }
          }, 3000);
        } else if (status === "failed" && errors?.length > 0) {
          setError(errors[0]);
          NotificationManager.error(errors[0]);
        } else if (!status) {
          setError(formatMessage({ id: "Unable to verify payment status" }));
        }
      } catch (err) {
        console.error("Payment status check error:", err);
        setError(err.message || formatMessage({ id: "Payment verification failed" }));
        setPaymentStatus("failed");
      }
    };

    checkStatus();
  }, [
    rentalId,
    paymentStatus_param,
    orderId,
    extensionId,
    isInstallment,
    paymentContext.rentalId,
    checkPaymentStatus,
    formatMessage,
    history,
    declineType,
  ]);

  // Check if we have no payment information at all
  useEffect(() => {
    if (!rentalId && !paymentContext.rentalId && !extensionId && !orderId) {
      setPaymentStatus("failed");
      setError(
        formatMessage({
          id: "No payment information found. Please return to the dashboard.",
        }),
      );
    }
  }, [rentalId, paymentContext.rentalId, extensionId, orderId, formatMessage]);

  const handleRetry = () => {
    sessionStorage.removeItem("paymentContext");
    history.goBack();
  };

  const handleClose = () => {
    sessionStorage.removeItem("paymentContext");
    const bookingId = rentalId || paymentContext.rentalId;
    if (bookingId) {
      history.push(`/cw/dashboard/bookings/${bookingId}`);
    } else {
      history.push("/cw");
    }
  };

  const handleManualCheck = async () => {
    const currentRentalId = rentalId || paymentContext.rentalId;

    if (orderId || currentRentalId) {
      try {
        const variables = extensionId
          ? { checkoutId: orderId, rentalExtensionId: extensionId }
          : { checkoutId: orderId, rentalId: currentRentalId };

        const result = await checkPaymentStatus({ variables });

        let status;
        let errors;

        if (extensionId) {
          status = result.data?.extensionGetPaymentStatus?.status;
          errors = result.data?.extensionGetPaymentStatus?.errors;
        } else if (isInstallment) {
          status = result.data?.installmentGetPaymentStatus?.status;
          errors = result.data?.installmentGetPaymentStatus?.errors;
        } else {
          status = result.data?.getPaymentStatus?.status;
          errors = result.data?.getPaymentStatus?.errors;
        }

        setPaymentStatus(status || "failed");

        if (status === "paid") {
          sessionStorage.removeItem("paymentContext");
          const bookingId = rentalId || paymentContext.rentalId;
          setTimeout(() => {
            if (bookingId) {
              history.push(`/cw/dashboard/bookings/${bookingId}`);
            } else {
              history.push("/cw");
            }
          }, 3000);
        } else if (status === "failed" && errors?.length > 0) {
          setError(errors[0]);
          NotificationManager.error(errors[0]);
        }
      } catch (err) {
        console.error("Manual payment status check error:", err);
        setError(err.message || formatMessage({ id: "Payment verification failed" }));
        setPaymentStatus("failed");
      }
    }
  };

  return (
    <div className="ecom-dashboard-wrapper">
      <Helmet>
        <title>{formatMessage({ id: "Payment Result" })}</title>
        <meta name="description" content="Tamara Payment Result Page" />
      </Helmet>
      <PageTitleBar
        title={<IntlMessages id="Payment Result" />}
        enableBreadCrumb
        match={location}
      />
      <div className="tamara-result-container">
        <PaymentStatusModal
          status={paymentStatus}
          error={error}
          onRetry={handleRetry}
          onClose={handleClose}
          onManualCheck={handleManualCheck}
          loading={loading}
        />
      </div>
    </div>
  );
};

export default TamaraResult;
