import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { AllAgencies } from "gql/queries/AgenciesNames.gql";
import { ActiveAgencies } from "gql/queries/Agencies.gql";
import { persist } from "constants/constants";

export default function AgenciesDropDown({
  loading,
  setSelectedAgency,
  selectedAgency,
  error,
  valueAttribute,
  multi,
  isActive,
  ...props
}) {
  const { data, loading: getAgencies } = useQuery(isActive ? ActiveAgencies : AllAgencies, {
    variables: isActive ? {} : { limit: 1000, isActive },
  });
  const { locale, formatMessage } = useIntl();

  const agenciesData = isActive ? data?.activeAgencies : data?.agencies?.collection;
  const options =
    agenciesData?.map((x) => ({
      value: x[valueAttribute || "id"],
      label: x[`${locale == "ar" ? "nameAr" : "nameEn"}`],
    })) || [];

  React.useEffect(() => {
    if (!selectedAgency) {
      onClear();
    }
  }, [selectedAgency]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  return (
    <Select
      className={`dropdown-select   ${error ? "selection-error" : ""}`}
      options={options}
      isMulti={multi}
      ref={selectInputRef}
      isClearable
      loadOptions={getAgencies || loading}
      defaultValue={options.find((optn) => `${optn.value}` === `${selectedAgency}`)}
      value={
        multi
          ? options?.filter((optn, index) => selectedAgency?.includes(+optn.value))
          : options.find((optn) => `${optn.value}` === `${+selectedAgency}`)
      }
      placeholder={formatMessage({ id: "Agencies" })}
      onChange={(selection) => {
        // if(selection === "all")
        if (multi) {
          setSelectedAgency(selection);
        } else {
          setSelectedAgency(+selection?.value);
        }
      }}
      noOptionsMessage={() => {
        if (getAgencies) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
      {...props}
    />
  );
}
AgenciesDropDown.propTypes = {
  valueAttribute: PropTypes.string,
  loading: PropTypes.bool,
  selectedAgency: PropTypes.string,
  setSelectedAlly: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
  isActive: PropTypes.bool,
};
