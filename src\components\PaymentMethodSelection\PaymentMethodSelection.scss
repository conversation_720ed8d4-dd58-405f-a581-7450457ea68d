.payment-methods-container {
  .payment-methods-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 20px;
  }

  .payment-method-card {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;

    &:hover:not(.disabled) {
      border-color: #007bff;
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
    }

    &.selected {
      border-color: #007bff;
      background: #f8f9ff;
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: #f5f5f5;
    }

    .payment-method-content {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .payment-method-header {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .radio-indicator {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: 2px solid #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .radio-selected {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #007bff;
      }

      .radio-unselected {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: transparent;
      }
    }

    .payment-method-icons {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .payment-method-icon {
      width: 50px;
      height: auto;
      object-fit: contain;
    }

    .payment-method-info {
      flex: 1;

      .payment-method-name {
        margin: 0 0 4px 0;
        font-weight: 600;
        color: #333;
        font-size: 16px;
      }

      .payment-method-description {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .wallet-section {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    background: #f9f9f9;

    .wallet-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      .wallet-icon {
        color: #007bff;
      }

      h6 {
        margin: 0;
        font-weight: 600;
        color: #333;
      }
    }

    .wallet-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .wallet-balance {
        display: flex;
        flex-direction: column;

        .balance-amount {
          font-size: 18px;
          font-weight: 700;
          color: #28a745;
        }

        .balance-label {
          font-size: 12px;
          color: #666;
        }
      }

      .wallet-toggle {
        display: flex;
        align-items: center;
        gap: 8px;

        .wallet-switch {
          position: relative;
          display: inline-block;
          width: 50px;
          height: 24px;

          input {
            opacity: 0;
            width: 0;
            height: 0;

            &:checked + .slider {
              background-color: #007bff;

              &:before {
                transform: translateX(26px);
              }
            }

            &:disabled + .slider {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }

          .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.3s;
            border-radius: 24px;

            &:before {
              position: absolute;
              content: "";
              height: 18px;
              width: 18px;
              left: 3px;
              bottom: 3px;
              background-color: white;
              transition: 0.3s;
              border-radius: 50%;
            }
          }
        }

        .wallet-toggle-label {
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }
      }
    }

    .wallet-usage-info {
      padding: 8px 12px;
      background: #e7f3ff;
      border-radius: 4px;
      border-left: 3px solid #007bff;

      small {
        font-size: 12px;
        line-height: 1.4;
      }
    }
  }
}

// Responsive design
@media (min-width: 768px) {
  .payment-methods-container {
    .payment-methods-grid {
      grid-template-columns: 1fr 1fr;
    }
  }
}

@media (min-width: 992px) {
  .payment-methods-container {
    .payment-methods-grid {
      grid-template-columns: 1fr 1fr 1fr;
    }
  }
}

// RTL Support
[dir="rtl"] {
  .payment-method-card {
    .payment-method-content {
      direction: rtl;
    }
  }

  .wallet-section {
    .wallet-info {
      direction: rtl;
    }

    .wallet-toggle {
      .wallet-switch {
        .slider {
          &:before {
            left: auto;
            right: 3px;
          }
        }

        input:checked + .slider:before {
          transform: translateX(-26px);
        }
      }
    }
  }
}
