import React, { useEffect, useRef } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import tamaraIcon from "../../assets/icons/tamara.svg";

const TamaraWidget = ({
  checkoutUrl,
  onSuccess,
  onFailure,
  onCancel,
  totalAmount,
  locale = "ar",
}) => {
  const { formatMessage } = useIntl();
  const widgetRef = useRef(null);
  const scriptRef = useRef(null);

  useEffect(() => {
    if (!checkoutUrl) return;

    // Redirect to Tamara checkout URL (like web app does)
    console.log("Redirecting to Tamara checkout:", checkoutUrl);

    // Add a small delay to show the loading state
    const timer = setTimeout(() => {
      window.location.href = checkoutUrl;
    }, 1000);

    // Cleanup function
    return () => {
      clearTimeout(timer);
    };
  }, [checkoutUrl]);

  return (
    <div className="tamara-widget-container">
      <div className="tamara-info mb-3">
        <div className="d-flex align-items-center justify-content-center mb-2">
          <img src={tamaraIcon} alt="Tamara" style={{ height: "32px", marginRight: "8px" }} />
          <span className="font-weight-bold">
            <FormattedMessage id="3 Payments interest free" />
          </span>
        </div>

        {totalAmount && (
          <div className="tamara-installment-preview text-center">
            <div
              className="tamara-installment-plan-widget"
              data-lang={locale}
              data-price={totalAmount}
              data-currency="SAR"
              data-color-type="default"
              data-country-code="SA"
              data-number-of-installments="3"
            />
          </div>
        )}
      </div>

      <div
        ref={widgetRef}
        id="tamara-checkout-widget"
        className="tamara-checkout-container"
        style={{
          minHeight: "400px",
          border: "1px solid #e0e0e0",
          borderRadius: "8px",
          padding: "40px 20px",
          backgroundColor: "#fafafa",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div className="text-center">
          <div
            className="spinner-border text-primary mb-3"
            role="status"
            style={{ width: "3rem", height: "3rem" }}
          >
            <span className="sr-only">
              <FormattedMessage id="Loading..." />
            </span>
          </div>
          <h5 className="mb-2">
            <FormattedMessage id="Redirecting to Tamara..." />
          </h5>
          <p className="text-muted">
            <FormattedMessage id="You will be redirected to Tamara to complete your payment" />
          </p>
          {checkoutUrl && (
            <div className="mt-3">
              <small className="text-muted">
                <FormattedMessage id="If you are not redirected automatically," />{" "}
                <a href={checkoutUrl} className="text-primary">
                  <FormattedMessage id="click here" />
                </a>
              </small>
            </div>
          )}
        </div>
      </div>

      <div className="tamara-footer mt-3">
        <small className="text-muted text-center d-block">
          <FormattedMessage id="Powered by Tamara" />
        </small>
      </div>
    </div>
  );
};

export default TamaraWidget;
